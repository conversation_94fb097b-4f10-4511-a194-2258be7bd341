"""
模型评估模块
提供模型性能评估和可视化功能
"""

from __future__ import annotations
import numpy as np
import pandas as pd
from typing import Optional, Dict, Any, Tuple
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sklearn.metrics import (
    roc_auc_score, roc_curve, precision_recall_curve,
    classification_report, confusion_matrix,
    accuracy_score, precision_score, recall_score, f1_score
)
from sklearn.pipeline import Pipeline
import matplotlib.pyplot as plt
import seaborn as sns
from utils.logger import get_logger

logger = get_logger("main")


def evaluate_model(model: Pipeline, X_test: pd.DataFrame, y_test: pd.Series,
                  threshold: float = 0.5) -> Dict[str, Any]:
    """
    评估模型性能

    Args:
        model: 训练好的模型
        X_test: 测试特征
        y_test: 测试标签
        threshold: 分类阈值

    Returns:
        评估指标字典
    """
    # 转换为二分类
    y_test_bin = (y_test > 0).astype(int)

    # 预测
    y_pred_proba = model.predict_proba(X_test)[:, 1]
    y_pred = (y_pred_proba >= threshold).astype(int)

    # 计算指标
    metrics = {
        'accuracy': accuracy_score(y_test_bin, y_pred),
        'precision': precision_score(y_test_bin, y_pred, zero_division=0),
        'recall': recall_score(y_test_bin, y_pred, zero_division=0),
        'f1_score': f1_score(y_test_bin, y_pred, zero_division=0),
        'roc_auc': roc_auc_score(y_test_bin, y_pred_proba),
        'threshold': threshold
    }

    # 混淆矩阵
    cm = confusion_matrix(y_test_bin, y_pred)
    metrics['confusion_matrix'] = cm

    # 分类报告
    metrics['classification_report'] = classification_report(
        y_test_bin, y_pred, output_dict=True, zero_division=0
    )

    logger.info("模型评估结果:")
    logger.info(f"准确率: {metrics['accuracy']:.4f}")
    logger.info(f"精确率: {metrics['precision']:.4f}")
    logger.info(f"召回率: {metrics['recall']:.4f}")
    logger.info(f"F1分数: {metrics['f1_score']:.4f}")
    logger.info(f"ROC AUC: {metrics['roc_auc']:.4f}")

    return metrics


def plot_roc_curve(model: Pipeline, X_test: pd.DataFrame, y_test: pd.Series,
                  save_path: Optional[str] = None) -> None:
    """
    绘制ROC曲线

    Args:
        model: 训练好的模型
        X_test: 测试特征
        y_test: 测试标签
        save_path: 保存路径
    """
    y_test_bin = (y_test > 0).astype(int)
    y_pred_proba = model.predict_proba(X_test)[:, 1]

    fpr, tpr, _ = roc_curve(y_test_bin, y_pred_proba)
    auc = roc_auc_score(y_test_bin, y_pred_proba)

    plt.figure(figsize=(8, 6))
    plt.plot(fpr, tpr, label=f'ROC Curve (AUC = {auc:.3f})')
    plt.plot([0, 1], [0, 1], 'k--', label='Random')
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title('ROC Curve')
    plt.legend()
    plt.grid(True)

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"ROC曲线已保存到: {save_path}")

    plt.show()


def plot_precision_recall_curve(model: Pipeline, X_test: pd.DataFrame, y_test: pd.Series,
                               save_path: Optional[str] = None) -> None:
    """
    绘制精确率-召回率曲线

    Args:
        model: 训练好的模型
        X_test: 测试特征
        y_test: 测试标签
        save_path: 保存路径
    """
    y_test_bin = (y_test > 0).astype(int)
    y_pred_proba = model.predict_proba(X_test)[:, 1]

    precision, recall, _ = precision_recall_curve(y_test_bin, y_pred_proba)

    plt.figure(figsize=(8, 6))
    plt.plot(recall, precision, label='PR Curve')
    plt.xlabel('Recall')
    plt.ylabel('Precision')
    plt.title('Precision-Recall Curve')
    plt.legend()
    plt.grid(True)

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"PR曲线已保存到: {save_path}")

    plt.show()


def plot_confusion_matrix(cm: np.ndarray, save_path: Optional[str] = None) -> None:
    """
    绘制混淆矩阵

    Args:
        cm: 混淆矩阵
        save_path: 保存路径
    """
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=['Negative', 'Positive'],
                yticklabels=['Negative', 'Positive'])
    plt.title('Confusion Matrix')
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"混淆矩阵已保存到: {save_path}")

    plt.show()


def plot_feature_importance(model: Pipeline, feature_names: list,
                          top_n: int = 20, save_path: Optional[str] = None) -> None:
    """
    绘制特征重要性

    Args:
        model: 训练好的模型
        feature_names: 特征名称列表
        top_n: 显示前N个重要特征
        save_path: 保存路径
    """
    # 获取特征重要性
    if hasattr(model.named_steps['classifier'], 'feature_importances_'):
        importance = model.named_steps['classifier'].feature_importances_
    elif hasattr(model.named_steps['classifier'], 'coef_'):
        importance = np.abs(model.named_steps['classifier'].coef_[0])
    else:
        logger.warning("模型不支持特征重要性分析")
        return

    # 创建重要性DataFrame
    importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': importance
    }).sort_values('importance', ascending=False).head(top_n)

    # 绘图
    plt.figure(figsize=(10, 8))
    sns.barplot(data=importance_df, x='importance', y='feature')
    plt.title(f'Top {top_n} Feature Importance')
    plt.xlabel('Importance')
    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"特征重要性图已保存到: {save_path}")

    plt.show()

    return importance_df


def threshold_analysis(model: Pipeline, X_test: pd.DataFrame, y_test: pd.Series,
                      thresholds: Optional[np.ndarray] = None) -> pd.DataFrame:
    """
    阈值分析

    Args:
        model: 训练好的模型
        X_test: 测试特征
        y_test: 测试标签
        thresholds: 阈值数组

    Returns:
        阈值分析结果DataFrame
    """
    if thresholds is None:
        thresholds = np.arange(0.1, 1.0, 0.05)

    y_test_bin = (y_test > 0).astype(int)
    y_pred_proba = model.predict_proba(X_test)[:, 1]

    results = []
    for threshold in thresholds:
        y_pred = (y_pred_proba >= threshold).astype(int)

        metrics = {
            'threshold': threshold,
            'accuracy': accuracy_score(y_test_bin, y_pred),
            'precision': precision_score(y_test_bin, y_pred, zero_division=0),
            'recall': recall_score(y_test_bin, y_pred, zero_division=0),
            'f1_score': f1_score(y_test_bin, y_pred, zero_division=0)
        }
        results.append(metrics)

    results_df = pd.DataFrame(results)

    # 找到最佳阈值
    best_f1_idx = results_df['f1_score'].idxmax()
    best_threshold = results_df.loc[best_f1_idx, 'threshold']

    logger.info(f"最佳F1阈值: {best_threshold:.3f}")
    logger.info(f"最佳F1分数: {results_df.loc[best_f1_idx, 'f1_score']:.4f}")

    return results_df


def plot_threshold_analysis(threshold_results: pd.DataFrame,
                          save_path: Optional[str] = None) -> None:
    """
    绘制阈值分析图

    Args:
        threshold_results: 阈值分析结果
        save_path: 保存路径
    """
    plt.figure(figsize=(12, 8))

    metrics = ['accuracy', 'precision', 'recall', 'f1_score']
    for metric in metrics:
        plt.plot(threshold_results['threshold'], threshold_results[metric],
                label=metric.replace('_', ' ').title(), marker='o')

    plt.xlabel('Threshold')
    plt.ylabel('Score')
    plt.title('Threshold Analysis')
    plt.legend()
    plt.grid(True)
    plt.xlim(0, 1)
    plt.ylim(0, 1)

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"阈值分析图已保存到: {save_path}")

    plt.show()


def comprehensive_evaluation(model: Pipeline, X_test: pd.DataFrame, y_test: pd.Series,
                           feature_names: list, output_dir: str = "evaluation_results") -> Dict:
    """
    综合评估

    Args:
        model: 训练好的模型
        X_test: 测试特征
        y_test: 测试标签
        feature_names: 特征名称
        output_dir: 输出目录

    Returns:
        评估结果字典
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    logger.info("开始综合评估...")

    # 基础评估
    metrics = evaluate_model(model, X_test, y_test)

    # 阈值分析
    threshold_results = threshold_analysis(model, X_test, y_test)

    # 绘制各种图表
    plot_roc_curve(model, X_test, y_test,
                  save_path=os.path.join(output_dir, "roc_curve.png"))

    plot_precision_recall_curve(model, X_test, y_test,
                               save_path=os.path.join(output_dir, "pr_curve.png"))

    plot_confusion_matrix(metrics['confusion_matrix'],
                         save_path=os.path.join(output_dir, "confusion_matrix.png"))

    importance_df = plot_feature_importance(model, feature_names,
                                          save_path=os.path.join(output_dir, "feature_importance.png"))

    plot_threshold_analysis(threshold_results,
                          save_path=os.path.join(output_dir, "threshold_analysis.png"))

    # 保存结果
    results = {
        'metrics': metrics,
        'threshold_analysis': threshold_results,
        'feature_importance': importance_df
    }

    # 保存到文件
    threshold_results.to_csv(os.path.join(output_dir, "threshold_analysis.csv"), index=False)
    if importance_df is not None:
        importance_df.to_csv(os.path.join(output_dir, "feature_importance.csv"), index=False)

    # 保存评估报告
    with open(os.path.join(output_dir, "evaluation_report.txt"), 'w', encoding='utf-8') as f:
        f.write("模型评估报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"准确率: {metrics['accuracy']:.4f}\n")
        f.write(f"精确率: {metrics['precision']:.4f}\n")
        f.write(f"召回率: {metrics['recall']:.4f}\n")
        f.write(f"F1分数: {metrics['f1_score']:.4f}\n")
        f.write(f"ROC AUC: {metrics['roc_auc']:.4f}\n\n")

        f.write("混淆矩阵:\n")
        f.write(str(metrics['confusion_matrix']) + "\n\n")

        f.write("分类报告:\n")
        f.write(str(metrics['classification_report']) + "\n")

    logger.info(f"评估结果已保存到: {output_dir}")

    return results

