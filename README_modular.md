# CryptoQuant 模块化量化交易系统

这是从单体脚手架 `chat/GPT5.py` 拆分而来的模块化量化交易系统。

## 项目结构

```
CryptoQuant/
├── main.py                    # 主入口文件
├── utils/                     # 工具模块
│   ├── helpers.py            # 通用工具函数
│   ├── config.py             # 配置管理
│   └── logger.py             # 日志系统
├── features/                  # 特征工程模块
│   ├── indicators.py         # 技术指标计算
│   ├── labeling.py           # 事件打标
│   └── build_features.py     # 特征构建
├── models/                    # 机器学习模块
│   ├── train.py              # 模型训练
│   ├── evaluate.py           # 模型评估
│   └── save_load.py          # 模型保存加载
├── strategy/                  # 策略模块
│   ├── base_strategy.py      # 基础策略
│   ├── ml_filter.py          # ML过滤策略
│   └── run_backtest.py       # 回测运行
├── optimization/              # 优化模块
│   ├── optuna_objective.py   # 优化目标函数
│   ├── run_optuna.py         # Optuna运行
│   └── visualize.py          # 结果可视化
└── live/                      # 实盘交易模块
    ├── broker.py             # 交易所接口
    ├── live_trade.py         # 实盘交易
    └── risk.py               # 风险管理
```

## 安装依赖

使用 uv 管理 Python 环境：

```bash
# 安装基础依赖
uv pip install pandas numpy scikit-learn joblib

# 安装回测依赖
uv pip install backtrader matplotlib

# 安装优化依赖
uv pip install optuna

# 安装实盘交易依赖
uv pip install ccxt

# 安装可视化依赖
uv pip install seaborn plotly
```

## 使用方法

### 1. 训练模式

训练机器学习模型并进行超参数优化：
【在 Windows 命令行中不需要使用反斜杠来分行，应该将所有参数写在同一行。】
```
python main.py --mode train --csv data/ETHUSDT_5m.csv --symbol ETHUSDT --timeframe 5m --study_trials 50
```


下面是原来的，如果你希望命令更容易阅读，可以使用 Windows 的 ^ 符号来分行:【python main.py --mode train ^】
```bash
python main.py --mode train \
    --csv data/ETHUSDT_5m.csv \
    --symbol ETHUSDT \
    --timeframe 5m \
    --study_trials 50
```


### 2. 回测模式

使用训练好的模型进行回测：
``` bash
uv run python main.py --mode backtest --csv data/ETHUSDT_5m.csv --symbol ETHUSDT --timeframe 5m --model_path models/ETHUSDT_5m_20250825_121309.pkl --prob_threshold 0.65
```

```bash
python main.py --mode backtest \
    --csv data/BTCUSDT_5m.csv \
    --symbol BTCUSDT \
    --timeframe 5m \
    --model_path models/BTCUSDT_5m_20240822_071234.pkl \
    --prob_threshold 0.65 \
    --ma_s 24 \
    --ma_l 128 \
    --tp_atr 3.0 \
    --sl_atr 1.5 \
    --delta 0.5
```

### 3. 实盘模式（谨慎使用）

```bash
python main.py --mode live \
    --exchange binanceusdm \
    --symbol BTC/USDT \
    --timeframe 5m \
    --model_path models/BTCUSDT_5m_20240822_071234.pkl \
    --prob_threshold 0.65 \
    --api_key YOUR_API_KEY \
    --api_secret YOUR_API_SECRET
```

## 模块说明

### utils/ - 工具模块
- **helpers.py**: 数据读取、技术指标计算等通用函数
- **config.py**: 统一配置管理，支持从文件和命令行参数加载
- **logger.py**: 日志系统，支持不同类型的日志器

### features/ - 特征工程模块
- **indicators.py**: 技术指标计算（均线、ATR、布林带、RSI等）
- **labeling.py**: 事件打标（三重障碍法、固定时间窗口等）
- **build_features.py**: 特征构建和合并

### models/ - 机器学习模块
- **train.py**: 模型训练，支持多种分类器和超参数调优
- **evaluate.py**: 模型评估，包含各种指标和可视化
- **save_load.py**: 模型持久化，支持元数据管理

### strategy/ - 策略模块
- **base_strategy.py**: 基础双均线+回踩策略
- **ml_filter.py**: 带ML过滤的策略和自适应策略
- **run_backtest.py**: 回测运行，支持批量回测和参数扫描

### optimization/ - 优化模块
- **optuna_objective.py**: 定义各种优化目标函数
- **run_optuna.py**: Optuna优化运行
- **visualize.py**: 优化结果可视化

### live/ - 实盘交易模块
- **broker.py**: 交易所接口封装
- **live_trade.py**: 实盘交易主循环
- **risk.py**: 风险管理逻辑

## 配置文件

可以使用JSON配置文件来管理参数：

```json
{
  "signal_params": {
    "ma_s": 24,
    "ma_l": 128,
    "tp_atr": 3.0,
    "sl_atr": 1.5,
    "delta": 0.5
  },
  "model_config": {
    "prob_threshold": 0.65,
    "model_type": "GradientBoostingClassifier"
  },
  "backtest_config": {
    "cash_start": 10000,
    "commission": 0.0007
  },
  "live_trading_config": {
    "exchange": "binanceusdm",
    "symbol": "BTC/USDT",
    "timeframe": "5m"
  }
}
```

使用配置文件：

```bash
python main.py --mode train --config config.json --csv data/BTCUSDT_5m.csv
```

## 数据格式

支持两种CSV数据格式：

1. **标准格式**: timestamp, open, high, low, close, volume
2. **Binance格式**: open_time, open, high, low, close, volume, close_time, ...

## 注意事项

1. **实盘交易风险**: 实盘交易有风险，建议先在沙盒环境测试
2. **数据质量**: 确保数据质量良好，无缺失值和异常值
3. **模型验证**: 充分验证模型性能后再用于实盘
4. **风险管理**: 设置合理的止损和仓位管理
5. **监控**: 实盘运行时需要持续监控

## 扩展开发

模块化设计便于扩展：

- 添加新的技术指标到 `features/indicators.py`
- 实现新的标注方法到 `features/labeling.py`
- 添加新的策略到 `strategy/` 目录
- 实现新的优化目标到 `optimization/optuna_objective.py`
- 添加新的风险管理规则到 `live/risk.py`

## 日志和结果

- 日志文件保存在 `logs/` 目录
- 模型文件保存在 `models/` 目录
- 回测结果保存为JSON文件
- 优化结果保存为JSON文件
- 评估图表保存在 `evaluation_results/` 目录

## 从原始脚手架迁移

如果你之前使用 `chat/GPT5.py`，可以按以下步骤迁移：

1. 将数据文件放到合适位置
2. 使用新的 `main.py` 重新训练模型
3. 对比新旧系统的回测结果
4. 根据需要调整参数配置

模块化系统提供了更好的可维护性、可测试性和可扩展性。
