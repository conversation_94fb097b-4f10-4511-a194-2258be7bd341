"""
日志系统模块
统一管理项目日志输出
"""

import logging
import os
from datetime import datetime
from typing import Optional


class Logger:
    """统一日志管理器"""

    def __init__(self, name: str = "CryptoQuant", log_dir: str = "logs"):
        self.name = name
        self.log_dir = log_dir
        self.logger = None
        self._setup_logger()

    def _setup_logger(self):
        """设置日志器"""
        # 创建日志目录
        os.makedirs(self.log_dir, exist_ok=True)

        # 创建logger
        self.logger = logging.getLogger(self.name)
        self.logger.setLevel(logging.INFO)

        # 避免重复添加handler
        if self.logger.handlers:
            return

        # 创建formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 控制台handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)

        # 文件handler
        log_file = os.path.join(self.log_dir, f"{self.name}_{datetime.now().strftime('%Y%m%d')}.log")
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)

    def info(self, message: str):
        """记录信息日志"""
        self.logger.info(message)

    def debug(self, message: str):
        """记录调试日志"""
        self.logger.debug(message)

    def warning(self, message: str):
        """记录警告日志"""
        self.logger.warning(message)

    def error(self, message: str):
        """记录错误日志"""
        self.logger.error(message)

    def critical(self, message: str):
        """记录严重错误日志"""
        self.logger.critical(message)


class BacktestLogger(Logger):
    """回测专用日志器"""

    def __init__(self, strategy_name: str = "Strategy", log_dir: str = "logs/backtest"):
        super().__init__(f"Backtest_{strategy_name}", log_dir)

    def log_trade(self, action: str, price: float, size: float, timestamp: str):
        """记录交易日志"""
        self.info(f"TRADE - {action}: Price={price:.4f}, Size={size:.4f}, Time={timestamp}")

    def log_signal(self, signal_type: str, direction: int, probability: float, timestamp: str):
        """记录信号日志"""
        self.info(f"SIGNAL - {signal_type}: Direction={direction}, Prob={probability:.3f}, Time={timestamp}")

    def log_performance(self, metrics: dict):
        """记录性能指标"""
        self.info("PERFORMANCE METRICS:")
        for key, value in metrics.items():
            self.info(f"  {key}: {value}")


class LiveTradingLogger(Logger):
    """实盘交易专用日志器"""

    def __init__(self, exchange: str = "Exchange", log_dir: str = "logs/live"):
        super().__init__(f"Live_{exchange}", log_dir)

    def log_order(self, order_type: str, symbol: str, side: str, amount: float, price: Optional[float] = None):
        """记录订单日志"""
        price_str = f", Price={price:.4f}" if price else ""
        self.info(f"ORDER - {order_type}: {symbol} {side} {amount:.4f}{price_str}")

    def log_position(self, symbol: str, size: float, entry_price: float, unrealized_pnl: float):
        """记录持仓日志"""
        self.info(f"POSITION - {symbol}: Size={size:.4f}, Entry={entry_price:.4f}, PnL={unrealized_pnl:.4f}")

    def log_balance(self, balance: dict):
        """记录账户余额"""
        self.info("BALANCE:")
        for currency, amount in balance.items():
            self.info(f"  {currency}: {amount:.4f}")

    def log_error(self, error_type: str, message: str):
        """记录错误日志"""
        self.error(f"ERROR - {error_type}: {message}")


# 全局日志器实例
main_logger = Logger("Main")
backtest_logger = BacktestLogger()
live_logger = LiveTradingLogger()


def get_logger(logger_type: str = "main") -> Logger:
    """获取指定类型的日志器"""
    if logger_type == "main":
        return main_logger
    elif logger_type == "backtest":
        return backtest_logger
    elif logger_type == "live":
        return live_logger
    else:
        return Logger(logger_type)

