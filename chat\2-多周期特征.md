下面我给你设计一个 **量化交易系统特征模板**，核心目标是：

* 交易主周期（例：5min）
* 同时引入更长周期（例：30min、1h）
* 特征尽量解释性强，方便结合 ML 和规则策略

---

# 📐 多周期特征模板

假设交易周期是 **5min K线**，我们要额外引入 **30min** 和 **1h** 的上下文信息。

---

## 1. 价格相对长均线位置

* `price_rel_ma30m = close_5m / MA_30m_long`
* `price_rel_ma1h = close_5m / MA_1h_long`
  👉 表示当前价格在长周期均线的相对位置（>1 在均线上，<1 在均线下）。

---

## 2. 均线斜率（趋势强度）

* `ma30m_slope = (MA_30m_long - MA_30m_long.shift(1)) / MA_30m_long.shift(1)`
* `ma1h_slope = (MA_1h_long - MA_1h_long.shift(1)) / MA_1h_long.shift(1)`
  👉 趋势方向和强弱，避免仅看均线绝对位置。

---

## 3. 短长周期交互（触碰/反弹信号）

* `cross_distance = (close_5m - MA_1h_long) / ATR_1h`
  👉 价格与长周期均线的标准化距离（除以波动率，避免不同品种不可比）。
* `shortMA_vs_longMA = MA_5m_short / MA_1h_long`
  👉 短周期均线是否贴近/穿越长周期均线。

---

## 4. 波动率/形态特征

* `volatility_ratio = ATR_5m / ATR_1h`
  👉 短期波动是否异常放大。
* `upper_shadow = (high_5m - max(open_5m, close_5m)) / (high_5m - low_5m + eps)`
  👉 触碰长周期均线后，影线上长短 → 是否“打到均线被压回”。

---

## 5. 成交量特征（Volume Confirmation）

* `volume_rel = volume_5m / SMA(volume_30m, n=10)`
  👉 当前成交量相对30min均量的比值。
* `vol_spike = (volume_5m - mean(volume_30m_last10)) / std(volume_30m_last10)`
  👉 是否发生成交量异常，配合均线触碰点判断真假突破。

---

## 6. 复合逻辑特征（策略可直接用）

* `is_near_ma = abs(close_5m - MA_1h_long) < k * ATR_5m`
  👉 判断是否“靠近关键长周期均线”。
* `trend_confirm = sign(ma1h_slope) == sign(MA_5m_short - MA_1h_long)`
  👉 趋势方向是否一致（避免逆势）。
* `bounce_prob = ML_model.predict([price_rel_ma1h, cross_distance, ma1h_slope, upper_shadow, volume_rel])`
  👉 ML 模型学习出来的“触碰长周期 MA 后是否反弹”的概率。

---

# 🔧 工程实现方式

1. **数据层**

   * 在 `data/` 里同时拉取 5m、30m、1h 的 K线
   * 用 `resample` 或 `merge_asof` 对齐到 5m 时间轴

2. **特征工程层（features/）**

   * 写一个 `FeatureBuilder` 类，输入不同周期的 DataFrame，自动生成上述特征
   * 输出一个对齐到 5m 的特征表

3. **策略/模型层**

   * 策略规则：

     * 只有 `is_near_ma == True` 且 `trend_confirm == True` 才允许触发
   * ML 模型：

     * 输入 `cross_distance, ma1h_slope, upper_shadow, volume_rel` 等
     * 输出 `bounce_prob` → 作为交易过滤器

---

✅ **总结**

* **低层特征**：均线位置、斜率、波动率、成交量
* **中层特征**：价格-均线交互（cross\_distance, is\_near\_ma）
* **高层特征**：复合逻辑 / ML 输出

这样设计的模板既能支持**纯规则策略**，也能支持**监督学习分类器**，同时保留了可解释性。

---

要不要我帮你写一份 **Python 版的 `FeatureBuilder` 类代码**，可以直接输入多周期 K线 DataFrame，然后一键生成这些特征？

好的👌 那我给你写一个 **Python 版 FeatureBuilder 类**，它可以：

* 输入：多周期 K 线（例如 5m、30m、1h），格式为 DataFrame（含 open, high, low, close, volume）。
* 自动对齐到主周期（默认 5m）。
* 输出：包含我刚才设计的特征表。

我会用 **pandas** 来实现，方便你接到你的量化回测框架里。

---

```python
import pandas as pd
import numpy as np

class FeatureBuilder:
    def __init__(self, df_5m, df_30m, df_1h, main_freq="5T"):
        """
        df_5m, df_30m, df_1h : DataFrame, index = datetime, columns = [open, high, low, close, volume]
        main_freq : 主周期，默认 5T (5分钟)
        """
        self.df_5m = df_5m.copy()
        self.df_30m = df_30m.copy()
        self.df_1h = df_1h.copy()
        self.main_freq = main_freq
        
        # 统一索引到 5min 时间轴
        self.df = self.df_5m.copy()

    def add_ma_features(self, df, windows=[20, 50, 100]):
        """计算均线"""
        for w in windows:
            df[f"ma_{w}"] = df["close"].rolling(w).mean()
        return df

    def add_slope_features(self, df, cols):
        """均线斜率 (相对变化率)"""
        for c in cols:
            df[f"{c}_slope"] = df[c].pct_change()
        return df

    def merge_multi_timeframes(self):
        """把 30m, 1h 的均线和ATR等特征 merge 到 5m"""
        def resample_df(df, rule):
            ohlcv = {
                "open": "first",
                "high": "max",
                "low": "min",
                "close": "last",
                "volume": "sum",
            }
            return df.resample(rule).apply(ohlcv).dropna()

        df_30m = resample_df(self.df_5m, "30T") if self.df_30m is None else self.df_30m
        df_1h = resample_df(self.df_5m, "1H") if self.df_1h is None else self.df_1h

        # 添加均线
        df_30m = self.add_ma_features(df_30m, [20, 50])
        df_1h = self.add_ma_features(df_1h, [20, 50])

        # 对齐到 5m 时间轴
        df_30m = df_30m.reindex(self.df.index, method="ffill")
        df_1h = df_1h.reindex(self.df.index, method="ffill")

        return df_30m, df_1h

    def add_features(self):
        df_30m, df_1h = self.merge_multi_timeframes()
        
        # 价格相对长均线位置
        self.df["price_rel_ma30m"] = self.df["close"] / df_30m["ma_50"]
        self.df["price_rel_ma1h"]  = self.df["close"] / df_1h["ma_50"]

        # 均线斜率
        self.df["ma30m_slope"] = df_30m["ma_50"].pct_change()
        self.df["ma1h_slope"]  = df_1h["ma_50"].pct_change()

        # 短长周期交互
        self.df["cross_distance"] = (self.df["close"] - df_1h["ma_50"]) / (self.df["close"].rolling(20).std())
        self.df["shortMA_vs_longMA"] = self.df["close"].rolling(10).mean() / df_1h["ma_50"]

        # 波动率/形态
        self.df["ATR_5m"] = (self.df["high"] - self.df["low"]).rolling(14).mean()
        self.df["ATR_1h"] = (df_1h["high"] - df_1h["low"]).rolling(14).mean()
        self.df["volatility_ratio"] = self.df["ATR_5m"] / self.df["ATR_1h"]
        self.df["upper_shadow"] = (self.df["high"] - self.df[["open", "close"]].max(axis=1)) / (self.df["high"] - self.df["low"] + 1e-9)

        # 成交量
        self.df["volume_rel"] = self.df["volume"] / df_30m["volume"].rolling(10).mean()
        self.df["vol_spike"]  = (self.df["volume"] - df_30m["volume"].rolling(10).mean()) / (df_30m["volume"].rolling(10).std() + 1e-9)

        # 复合逻辑特征
        self.df["is_near_ma"] = (abs(self.df["close"] - df_1h["ma_50"]) < 0.5 * self.df["ATR_5m"]).astype(int)
        self.df["trend_confirm"] = ((np.sign(self.df["ma1h_slope"]) == np.sign(self.df["shortMA_vs_longMA"] - 1)).astype(int))

        return self.df.dropna()

```

---

## 🔧 使用示例

```python
# 假设你已经有 df_5m, df_30m, df_1h 三个 DataFrame
fb = FeatureBuilder(df_5m, df_30m, df_1h)
features = fb.add_features()

print(features.tail(10))
```

---

这样你会得到一个 **对齐到 5m 的特征表**，里面包含：

* `price_rel_ma30m, price_rel_ma1h`
* `ma30m_slope, ma1h_slope`
* `cross_distance, shortMA_vs_longMA`
* `volatility_ratio, upper_shadow`
* `volume_rel, vol_spike`
* `is_near_ma, trend_confirm`

这些就可以直接丢给你的策略逻辑或 ML 模型。

