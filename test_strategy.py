"""
测试策略参数传递是否正确
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 可选依赖
try:
    import backtrader as bt
except ImportError:
    bt = None

from strategy.ml_filter import MLFilterStrategy

# 测试参数
test_params = {
    'ma_s': 24,
    'ma_l': 128,
    'atr_n': 14,
    'tp_atr': 3.0,
    'sl_atr': 1.5,
    'max_hold_bars': 24,
    'delta': 0.5,
    'prob_threshold': 0.65,
    'model_path': None,
    'commission': 0.0007,
    'cash_start': 10000,
    'risk_perc': 0.003
}

print("测试策略参数...")

try:
    # 尝试创建策略实例（模拟Backtrader的行为）
    # 注意：这里我们不能直接实例化，因为需要Backtrader环境
    # 但我们可以检查参数定义
    
    strategy_class = MLFilterStrategy
    
    # 检查策略类的params属性
    print(f"策略类: {strategy_class.__name__}")

    if bt is None:
        print("❌ Backtrader 未安装，无法完整测试")
        exit(1)

    # Backtrader 策略的参数检查
    if hasattr(strategy_class, 'params'):
        print("✅ 策略类有params属性")

        # 尝试访问参数（Backtrader的方式）
        try:
            # 检查参数是否可以通过 getattr 访问
            print("\n策略类定义的参数:")
            param_names = []

            # 尝试不同的方式获取参数
            if hasattr(strategy_class.params, '_getpairs'):
                # Backtrader 的参数对
                pairs = strategy_class.params._getpairs()
                for name, value in pairs:
                    print(f"  {name}: {value}")
                    param_names.append(name)
            elif hasattr(strategy_class.params, '__dict__'):
                # 直接访问字典
                for name, value in strategy_class.params.__dict__.items():
                    if not name.startswith('_'):
                        print(f"  {name}: {value}")
                        param_names.append(name)

            # 检查缺失的参数
            missing_params = [p for p in test_params.keys() if p not in param_names]
            if missing_params:
                print(f"\n❌ 策略类缺少以下参数定义: {missing_params}")
            else:
                print("\n✅ 所有参数都在策略类中正确定义")

        except Exception as e:
            print(f"❌ 访问参数时出错: {e}")

            # 备用方法：直接检查源代码中的params定义
            print("\n尝试直接检查源代码中的params定义...")
            import inspect
            source = inspect.getsource(strategy_class)
            if 'commission' in source:
                print("✅ 在源代码中找到 'commission' 参数")
            else:
                print("❌ 在源代码中未找到 'commission' 参数")
    else:
        print("❌ 策略类没有params属性")
        
except Exception as e:
    print(f"❌ 测试失败: {e}")
