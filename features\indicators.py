"""
技术指标计算模块
包含双均线、ATR、布林带、RSI等技术指标
"""

from __future__ import annotations
import numpy as np
import pandas as pd
from typing import Optional
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config import SignalParams
from utils.helpers import ema, atr, cross_over, cross_under


def build_signals(df: pd.DataFrame, p: SignalParams) -> pd.DataFrame:
    """
    构建交易信号

    Args:
        df: OHLCV数据
        p: 信号参数配置

    Returns:
        包含信号的DataFrame
    """
    out = df.copy()

    # 计算均线
    out["ema_s"] = ema(out["close"], p.ma_s)
    out["ema_l"] = ema(out["close"], p.ma_l)

    # 计算ATR
    out["atr"] = atr(out, p.atr_n)

    # 计算方向
    out["dir"] = np.sign(out["ema_s"] - out["ema_l"]).fillna(0)

    # 趋势腿信号
    out["tl_long"] = cross_over(out["ema_s"], out["ema_l"]).astype(int)
    out["tl_short"] = cross_under(out["ema_s"], out["ema_l"]).astype(int)

    # 回踩腿信号：在有方向时，z = |price-ema_s|/ATR <= delta
    z = (out["close"] - out["ema_s"]).abs() / (out["atr"] + 1e-12)
    out["pl_trigger"] = ((out["dir"] != 0) & (z <= p.delta)).astype(int)

    return out


def calculate_bollinger_bands(df: pd.DataFrame, period: int = 20, std_dev: float = 2.0) -> pd.DataFrame:
    """
    计算布林带指标

    Args:
        df: OHLCV数据
        period: 计算周期
        std_dev: 标准差倍数

    Returns:
        包含布林带的DataFrame
    """
    result = df.copy()

    # 中轨（移动平均线）
    result['bb_middle'] = result['close'].rolling(window=period).mean()

    # 标准差
    std = result['close'].rolling(window=period).std()

    # 上轨和下轨
    result['bb_upper'] = result['bb_middle'] + (std * std_dev)
    result['bb_lower'] = result['bb_middle'] - (std * std_dev)

    # 布林带宽度
    result['bb_width'] = result['bb_upper'] - result['bb_lower']

    # 价格在布林带中的位置（%B）
    result['bb_percent'] = (result['close'] - result['bb_lower']) / (result['bb_width'] + 1e-12)

    return result


def calculate_rsi(df: pd.DataFrame, period: int = 14) -> pd.Series:
    """
    计算RSI指标

    Args:
        df: OHLCV数据
        period: 计算周期

    Returns:
        RSI序列
    """
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()

    rs = gain / (loss + 1e-12)
    rsi = 100 - (100 / (1 + rs))

    return rsi


def calculate_macd(df: pd.DataFrame, fast: int = 12, slow: int = 26, signal: int = 9) -> pd.DataFrame:
    """
    计算MACD指标

    Args:
        df: OHLCV数据
        fast: 快线周期
        slow: 慢线周期
        signal: 信号线周期

    Returns:
        包含MACD的DataFrame
    """
    result = df.copy()

    # 计算快慢EMA
    ema_fast = ema(result['close'], fast)
    ema_slow = ema(result['close'], slow)

    # MACD线
    result['macd'] = ema_fast - ema_slow

    # 信号线
    result['macd_signal'] = ema(result['macd'], signal)

    # 柱状图
    result['macd_histogram'] = result['macd'] - result['macd_signal']

    return result


def calculate_stochastic(df: pd.DataFrame, k_period: int = 14, d_period: int = 3) -> pd.DataFrame:
    """
    计算随机指标KD

    Args:
        df: OHLCV数据
        k_period: K值计算周期
        d_period: D值平滑周期

    Returns:
        包含KD指标的DataFrame
    """
    result = df.copy()

    # 计算最高价和最低价
    high_max = result['high'].rolling(window=k_period).max()
    low_min = result['low'].rolling(window=k_period).min()

    # K值
    result['stoch_k'] = 100 * (result['close'] - low_min) / (high_max - low_min + 1e-12)

    # D值（K值的移动平均）
    result['stoch_d'] = result['stoch_k'].rolling(window=d_period).mean()

    return result


def calculate_volume_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """
    计算成交量指标

    Args:
        df: OHLCV数据

    Returns:
        包含成交量指标的DataFrame
    """
    result = df.copy()

    # 成交量移动平均
    result['volume_ma'] = result['volume'].rolling(window=20).mean()

    # 成交量比率
    result['volume_ratio'] = result['volume'] / (result['volume_ma'] + 1e-12)

    # 价量指标（OBV）
    price_change = result['close'].diff()
    volume_direction = np.where(price_change > 0, result['volume'],
                               np.where(price_change < 0, -result['volume'], 0))
    result['obv'] = volume_direction.cumsum()

    return result


def calculate_momentum_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """
    计算动量指标

    Args:
        df: OHLCV数据

    Returns:
        包含动量指标的DataFrame
    """
    result = df.copy()

    # 价格动量
    result['momentum_1'] = result['close'].pct_change(1)
    result['momentum_5'] = result['close'].pct_change(5)
    result['momentum_10'] = result['close'].pct_change(10)

    # 价格变化率
    result['roc_5'] = (result['close'] / result['close'].shift(5) - 1) * 100
    result['roc_10'] = (result['close'] / result['close'].shift(10) - 1) * 100

    # 威廉指标
    high_max = result['high'].rolling(window=14).max()
    low_min = result['low'].rolling(window=14).min()
    result['williams_r'] = -100 * (high_max - result['close']) / (high_max - low_min + 1e-12)

    return result


def add_all_indicators(df: pd.DataFrame, signal_params: Optional[SignalParams] = None) -> pd.DataFrame:
    """
    添加所有技术指标

    Args:
        df: OHLCV数据
        signal_params: 信号参数配置

    Returns:
        包含所有指标的DataFrame
    """
    if signal_params is None:
        signal_params = SignalParams()

    # 基础信号
    result = build_signals(df, signal_params)

    # 布林带
    result = calculate_bollinger_bands(result)

    # RSI
    result['rsi'] = calculate_rsi(result)

    # MACD
    macd_data = calculate_macd(result)
    result['macd'] = macd_data['macd']
    result['macd_signal'] = macd_data['macd_signal']
    result['macd_histogram'] = macd_data['macd_histogram']

    # 随机指标
    stoch_data = calculate_stochastic(result)
    result['stoch_k'] = stoch_data['stoch_k']
    result['stoch_d'] = stoch_data['stoch_d']

    # 成交量指标
    result = calculate_volume_indicators(result)

    # 动量指标
    result = calculate_momentum_indicators(result)

    return result

