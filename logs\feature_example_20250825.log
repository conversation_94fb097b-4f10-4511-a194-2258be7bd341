2025-08-25 12:52:46 - feature_example - INFO - 开始特征一致性使用示例
2025-08-25 12:52:46 - feature_example - INFO - === 示例1：基本使用方法 ===
2025-08-25 12:52:46 - feature_example - INFO - 特征数量: 33
2025-08-25 12:52:46 - feature_example - INFO - 特征名称: ['ma_diff', 'ma_ratio', 'ma_slope_s', 'ma_slope_l', 'atr', 'atr_ratio', 'bb_width', 'bb_percent', 'ret_1', 'ret_5', 'ret_10', 'rsi', 'rsi_oversold', 'rsi_overbought', 'macd', 'macd_signal', 'macd_histogram', 'volume_ratio', 'dir', 'hour', 'dow', 'month', 'price_position_20', 'price_position_50', 'volatility_5', 'volatility_20', 'price_channel_20', 'trend_strength', 'momentum_divergence', 'volume_price_trend', 'volatility_breakout', 'price_acceleration', 'ma_distance_normalized']
2025-08-25 12:52:46 - feature_example - INFO - 数据形状: (500, 33)
2025-08-25 12:52:46 - feature_example - INFO - === 示例2：自定义特征 ===
2025-08-25 12:52:46 - feature_example - INFO - 包含自定义特征的特征数量: 35
2025-08-25 12:52:46 - feature_example - INFO - 自定义特征: ['price_volatility_ratio', 'volume_price_correlation']
2025-08-25 12:52:46 - feature_example - INFO - === 示例3：特征选择配置 ===
2025-08-25 12:52:46 - feature_example - INFO - 配置信息:
2025-08-25 12:52:46 - feature_example - INFO -   基础特征: True
2025-08-25 12:52:46 - feature_example - INFO -   高级特征: True
2025-08-25 12:52:46 - feature_example - INFO -   自定义特征: ['bollinger_squeeze', 'volume_price_confirmation']
2025-08-25 12:52:46 - feature_example - INFO -   特征选择方法: importance
2025-08-25 12:52:46 - feature_example - INFO -   最大特征数: 20
2025-08-25 12:52:46 - feature_example - INFO -   期望特征数量: 35
2025-08-25 12:52:46 - feature_example - INFO - === 示例4：配置模板对比 ===
2025-08-25 12:52:46 - feature_example - INFO - MINIMAL 配置:
2025-08-25 12:52:46 - feature_example - INFO -   期望特征数量: 26
2025-08-25 12:52:46 - feature_example - INFO -   基础特征: True
2025-08-25 12:52:46 - feature_example - INFO -   高级特征: False
2025-08-25 12:52:46 - feature_example - INFO -   自定义特征数量: 0
2025-08-25 12:52:46 - feature_example - INFO - 
2025-08-25 12:52:46 - feature_example - INFO - STANDARD 配置:
2025-08-25 12:52:46 - feature_example - INFO -   期望特征数量: 33
2025-08-25 12:52:46 - feature_example - INFO -   基础特征: True
2025-08-25 12:52:46 - feature_example - INFO -   高级特征: True
2025-08-25 12:52:46 - feature_example - INFO -   自定义特征数量: 0
2025-08-25 12:52:46 - feature_example - INFO - 
2025-08-25 12:52:46 - feature_example - INFO - ENHANCED 配置:
2025-08-25 12:52:46 - feature_example - INFO -   期望特征数量: 37
2025-08-25 12:52:46 - feature_example - INFO -   基础特征: True
2025-08-25 12:52:46 - feature_example - INFO -   高级特征: True
2025-08-25 12:52:46 - feature_example - INFO -   自定义特征数量: 4
2025-08-25 12:52:46 - feature_example - INFO - 
2025-08-25 12:52:46 - feature_example - INFO - COMPREHENSIVE 配置:
2025-08-25 12:52:46 - feature_example - INFO -   期望特征数量: 42
2025-08-25 12:52:46 - feature_example - INFO -   基础特征: True
2025-08-25 12:52:46 - feature_example - INFO -   高级特征: True
2025-08-25 12:52:46 - feature_example - INFO -   自定义特征数量: 9
2025-08-25 12:52:46 - feature_example - INFO - 
2025-08-25 12:52:46 - feature_example - INFO - OPTIMIZED 配置:
2025-08-25 12:52:46 - feature_example - INFO -   期望特征数量: 36
2025-08-25 12:52:46 - feature_example - INFO -   基础特征: True
2025-08-25 12:52:46 - feature_example - INFO -   高级特征: True
2025-08-25 12:52:46 - feature_example - INFO -   自定义特征数量: 3
2025-08-25 12:52:46 - feature_example - INFO -   特征选择: importance
2025-08-25 12:52:46 - feature_example - INFO - 
2025-08-25 12:52:46 - feature_example - INFO - === 示例5：保存和加载配置 ===
2025-08-25 12:52:46 - feature_example - INFO - 配置已保存到: temp_feature_config.json
2025-08-25 12:52:46 - feature_example - INFO - 配置已加载，特征数量: 35
2025-08-25 12:52:46 - feature_example - INFO - 临时配置文件已删除
2025-08-25 12:52:46 - feature_example - INFO - === 示例6：特征一致性检查 ===
2025-08-25 12:52:46 - feature_example - INFO - DataFrame方法特征数量: 33
2025-08-25 12:52:46 - feature_example - INFO - 特征名称: ['ma_diff', 'ma_ratio', 'ma_slope_s', 'ma_slope_l', 'atr', 'atr_ratio', 'bb_width', 'bb_percent', 'ret_1', 'ret_5', 'ret_10', 'rsi', 'rsi_oversold', 'rsi_overbought', 'macd', 'macd_signal', 'macd_histogram', 'volume_ratio', 'dir', 'hour', 'dow', 'month', 'price_position_20', 'price_position_50', 'volatility_5', 'volatility_20', 'price_channel_20', 'trend_strength', 'momentum_divergence', 'volume_price_trend', 'volatility_breakout', 'price_acceleration', 'ma_distance_normalized']
2025-08-25 12:52:46 - feature_example - INFO - 
特征统计信息:
2025-08-25 12:52:46 - feature_example - INFO -   ma_diff: 均值=-2.2021, 标准差=2.1006
2025-08-25 12:52:46 - feature_example - INFO -   ma_ratio: 均值=-0.0231, 标准差=0.0219
2025-08-25 12:52:46 - feature_example - INFO -   ma_slope_s: 均值=-0.0346, 标准差=0.1603
2025-08-25 12:52:46 - feature_example - INFO -   ma_slope_l: 均值=-0.0409, 标准差=0.0510
2025-08-25 12:52:46 - feature_example - INFO -   atr: 均值=1.1451, 标准差=0.1661
2025-08-25 12:52:46 - feature_example - INFO -   atr_ratio: 均值=0.0124, 标准差=0.0018
2025-08-25 12:52:46 - feature_example - INFO -   bb_width: 均值=5.0636, 标准差=2.6898
2025-08-25 12:52:46 - feature_example - INFO -   bb_percent: 均值=0.4118, 标准差=0.3282
2025-08-25 12:52:46 - feature_example - INFO -   ret_1: 均值=-0.0004, 标准差=0.0093
2025-08-25 12:52:46 - feature_example - INFO -   ret_5: 均值=-0.0019, 标准差=0.0186
2025-08-25 12:52:46 - feature_example - INFO - ✅ 所有示例运行完成!
