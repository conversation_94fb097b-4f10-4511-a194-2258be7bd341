"""
用户自定义特征模块
提供用户自定义特征的示例和接口
"""

from __future__ import annotations
import numpy as np
import pandas as pd
from typing import Dict, Callable


def bollinger_squeeze(df: pd.DataFrame) -> pd.Series:
    """
    布林带挤压指标
    当布林带宽度小于历史平均值时，表示市场处于低波动状态
    """
    bb_width = (df["close"].rolling(20).std() * 2)
    bb_width_ma = bb_width.rolling(50).mean()
    return (bb_width < bb_width_ma * 0.8).astype(float)


def volume_price_confirmation(df: pd.DataFrame) -> pd.Series:
    """
    成交量价格确认指标
    价格上涨且成交量放大时为1，价格下跌且成交量放大时为-1
    """
    price_change = df["close"].pct_change()
    volume_ratio = df["volume"] / df["volume"].rolling(20).mean()
    
    confirmation = pd.Series(0.0, index=df.index)
    confirmation[(price_change > 0) & (volume_ratio > 1.2)] = 1.0
    confirmation[(price_change < 0) & (volume_ratio > 1.2)] = -1.0
    
    return confirmation


def support_resistance_distance(df: pd.DataFrame) -> pd.Series:
    """
    支撑阻力位距离
    计算当前价格到最近支撑/阻力位的距离
    """
    high_20 = df["high"].rolling(20).max()
    low_20 = df["low"].rolling(20).min()
    
    # 简化计算：到最高点和最低点的距离
    distance_to_high = (high_20 - df["close"]) / df["close"]
    distance_to_low = (df["close"] - low_20) / df["close"]
    
    # 返回到最近支撑/阻力位的最小距离
    return np.minimum(distance_to_high, distance_to_low)


def trend_consistency(df: pd.DataFrame) -> pd.Series:
    """
    趋势一致性指标
    多个时间周期的趋势方向是否一致
    """
    # 短期趋势（5日均线斜率）
    ma5_slope = df["close"].rolling(5).mean().diff()
    # 中期趋势（20日均线斜率）
    ma20_slope = df["close"].rolling(20).mean().diff()
    # 长期趋势（50日均线斜率）
    ma50_slope = df["close"].rolling(50).mean().diff()
    
    # 计算趋势一致性
    trend_signs = pd.DataFrame({
        'short': np.sign(ma5_slope),
        'medium': np.sign(ma20_slope),
        'long': np.sign(ma50_slope)
    })
    
    # 当所有趋势方向一致时返回1，否则返回一致性比例
    consistency = (trend_signs.sum(axis=1) / 3).abs()
    return consistency


def momentum_divergence_advanced(df: pd.DataFrame) -> pd.Series:
    """
    高级动量背离指标
    价格创新高但动量指标未创新高时产生背离信号
    """
    # 计算价格的相对强弱
    price_roc = df["close"].pct_change(10)
    
    # 计算动量指标（简化的RSI）
    delta = df["close"].diff()
    gain = (delta.where(delta > 0, 0)).rolling(14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    
    # 检测背离
    price_high = df["close"].rolling(20).max()
    rsi_high = rsi.rolling(20).max()
    
    # 当价格创新高但RSI未创新高时产生背离信号
    price_new_high = df["close"] == price_high
    rsi_not_new_high = rsi < rsi_high * 0.95
    
    divergence = (price_new_high & rsi_not_new_high).astype(float)
    return divergence


def volatility_regime(df: pd.DataFrame) -> pd.Series:
    """
    波动率状态指标
    识别当前市场是处于高波动还是低波动状态
    """
    # 计算滚动波动率
    returns = df["close"].pct_change()
    volatility = returns.rolling(20).std()
    
    # 计算波动率的分位数
    vol_quantile = volatility.rolling(252).rank(pct=True)  # 一年的分位数
    
    # 定义波动率状态：0=低波动，1=中等波动，2=高波动
    regime = pd.Series(0.0, index=df.index)
    regime[vol_quantile > 0.33] = 1.0
    regime[vol_quantile > 0.67] = 2.0
    
    return regime


def market_microstructure(df: pd.DataFrame) -> pd.Series:
    """
    市场微观结构指标
    基于价格行为分析市场微观结构特征
    """
    # 计算上下影线比例
    body = abs(df["close"] - df["open"])
    upper_shadow = df["high"] - np.maximum(df["open"], df["close"])
    lower_shadow = np.minimum(df["open"], df["close"]) - df["low"]
    
    # 影线比例
    total_range = df["high"] - df["low"]
    shadow_ratio = (upper_shadow + lower_shadow) / (total_range + 1e-12)
    
    return shadow_ratio


# 预定义的自定义特征字典
CUSTOM_FEATURES: Dict[str, Callable[[pd.DataFrame], pd.Series]] = {
    "bollinger_squeeze": bollinger_squeeze,
    "volume_price_confirmation": volume_price_confirmation,
    "support_resistance_distance": support_resistance_distance,
    "trend_consistency": trend_consistency,
    "momentum_divergence_advanced": momentum_divergence_advanced,
    "volatility_regime": volatility_regime,
    "market_microstructure": market_microstructure,
}


def get_custom_feature_function(name: str) -> Callable[[pd.DataFrame], pd.Series]:
    """
    获取自定义特征函数
    
    Args:
        name: 特征名称
        
    Returns:
        特征计算函数
        
    Raises:
        KeyError: 如果特征名称不存在
    """
    if name not in CUSTOM_FEATURES:
        raise KeyError(f"Custom feature '{name}' not found. Available features: {list(CUSTOM_FEATURES.keys())}")
    
    return CUSTOM_FEATURES[name]


def list_available_features() -> list:
    """
    列出所有可用的自定义特征
    
    Returns:
        特征名称列表
    """
    return list(CUSTOM_FEATURES.keys())


def add_custom_feature(name: str, func: Callable[[pd.DataFrame], pd.Series]):
    """
    添加新的自定义特征
    
    Args:
        name: 特征名称
        func: 特征计算函数
    """
    CUSTOM_FEATURES[name] = func


# 示例：如何创建自己的特征函数
def create_custom_feature_example():
    """
    创建自定义特征的示例
    """
    def my_custom_feature(df: pd.DataFrame) -> pd.Series:
        """
        我的自定义特征：价格相对于20日均线的位置
        """
        ma20 = df["close"].rolling(20).mean()
        return (df["close"] - ma20) / ma20
    
    # 添加到特征字典
    add_custom_feature("price_relative_to_ma20", my_custom_feature)
    
    return my_custom_feature


if __name__ == "__main__":
    # 示例用法
    print("可用的自定义特征:")
    for feature_name in list_available_features():
        print(f"- {feature_name}")
    
    # 创建示例特征
    create_custom_feature_example()
    print(f"\n添加自定义特征后，总共有 {len(CUSTOM_FEATURES)} 个特征")
