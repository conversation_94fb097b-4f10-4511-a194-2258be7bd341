之前写的 **`train_dataset.py`** 主要是一个 **数据加载脚手架**：

* 假设你已经有了 **带特征和标签的 DataFrame**（比如 csv 或预处理好的表）。
* 它负责把数据 **切分成训练集 / 验证集 / 测试集**，并包装成 `torch Dataset` 或 `sklearn` 能直接用的格式。
* 核心功能是 **喂模型**。

---

而这次写的 **`features/build_features.py`** 属于 **特征工程阶段**：

* 从最原始的 **行情数据 (open, high, low, close, volume)** 出发。
* 计算各种 **技术指标、统计特征**。
* 集成了 `add_labels()`，直接在 **特征构建阶段打标签**。
* 最后返回 `(X, y)`，供 **train\_dataset** 再去加载/划分/训练。

---

📌 总结区别：

* `features/build_features.py` → **负责特征生成 + 打标签**
* `train_dataset.py` → **负责数据集切分 + 打包成模型能用的 Dataset**

换句话说：

👉 **`build_features.py` 是工厂**，生产 `(X, y)` 原料。
👉 **`train_dataset.py` 是物流仓库**，把原料打包配送给模型。

---

帮你把 `features/build_features.py` 和 `train_dataset.py` 串起来，写一个统一的数据管道 `data_pipeline.py`。这样你只要调用一个函数，就能从 **原始 CSV → 特征工程 + 打标签 → 划分数据集**，最后直接丢给模型。

我会在代码里注明文件路径，方便你理解结构。

---

```python
# 文件位置: data_pipeline.py

import pandas as pd
from pathlib import Path

# 引入你之前写好的模块
from features.build_features import build_features_and_labels  # 特征工程 + 打标签
from train_dataset import create_datasets  # 切分训练/验证/测试集

def run_pipeline(
    raw_data_path: str,
    output_dir: str = "datasets/processed",
    test_size: float = 0.2,
    valid_size: float = 0.1,
    label_horizon: int = 30
):
    """
    从原始行情数据出发，完整跑通数据管道：
    1. 特征工程 + 打标签
    2. 生成 train/valid/test 数据集
    3. 保存到本地
    """

    # 1. 加载原始 K 线数据
    df = pd.read_csv(raw_data_path, parse_dates=["datetime"])
    print(f"[INFO] 原始数据加载完成: {df.shape}")

    # 2. 生成特征 + 标签
    df_features = build_features_and_labels(df, horizon=label_horizon)
    print(f"[INFO] 特征工程完成: {df_features.shape}")

    # 3. 创建数据集 (train/valid/test)
    datasets = create_datasets(
        df_features,
        test_size=test_size,
        valid_size=valid_size
    )

    # 4. 保存处理好的数据
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    for split, (X, y) in datasets.items():
        X.to_csv(Path(output_dir) / f"X_{split}.csv", index=False)
        y.to_csv(Path(output_dir) / f"y_{split}.csv", index=False)
        print(f"[INFO] {split} 数据集保存完成: X_{split}.csv, y_{split}.csv")

    return datasets


if __name__ == "__main__":
    # 示例运行
    datasets = run_pipeline(
        raw_data_path="datasets/raw/BTCUSDT_1m.csv",  # 输入的原始K线
        output_dir="datasets/processed",
        test_size=0.2,
        valid_size=0.1,
        label_horizon=30  # 打标签时用的 "向前看窗口"
    )

    # datasets["train"], datasets["valid"], datasets["test"] 就能直接拿来喂模型
```

---

这样你就能直接：

```bash
python data_pipeline.py
```

然后就能在 `datasets/processed/` 下得到 `X_train.csv`, `y_train.csv` 等文件，随时丢给模型训练。


