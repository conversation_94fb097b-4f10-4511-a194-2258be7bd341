帮你写一个 **集成 `add_labels()` 的 `features/build_features.py`**，这个文件的定位就是：

1. 从原始 K 线数据中构建特征（移动平均、波动率、成交量指标等）。
2. 调用 `features/labeling.py:add_labels()` 给数据加标签。
3. 输出 `(X, y)`，方便直接喂给训练脚本。

---

```python
# features/build_features.py
"""
特征构建模块
---------------------------------
从原始行情数据构建特征，并集成打标。
"""

import pandas as pd
import numpy as np

from features.labeling import add_labels


def compute_basic_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    构建基础技术指标特征

    Parameters
    ----------
    df : pd.DataFrame
        必须包含 ['open','high','low','close','volume']

    Returns
    -------
    pd.DataFrame
        带特征的 DataFrame
    """
    df = df.copy()

    # 移动均线
    df["ma_5"] = df["close"].rolling(5).mean()
    df["ma_10"] = df["close"].rolling(10).mean()
    df["ma_20"] = df["close"].rolling(20).mean()

    # 均线差
    df["ma_5_10"] = df["ma_5"] - df["ma_10"]
    df["ma_10_20"] = df["ma_10"] - df["ma_20"]

    # 波动率 (简单近似: rolling std)
    df["volatility"] = df["close"].pct_change().rolling(20).std()

    # 成交量特征
    df["vol_ma_5"] = df["volume"].rolling(5).mean()
    df["vol_ma_20"] = df["volume"].rolling(20).mean()
    df["vol_ratio"] = df["volume"] / (df["vol_ma_20"] + 1e-9)

    # 动量因子
    df["momentum_5"] = df["close"] / df["close"].shift(5) - 1
    df["momentum_10"] = df["close"] / df["close"].shift(10) - 1

    return df


def build_dataset(df: pd.DataFrame,
                  use_soft: bool = True,
                  use_triple_barrier: bool = False,
                  horizon: int = 20,
                  pt_sl: tuple = (1, 1),
                  max_holding: int = 20) -> tuple:
    """
    构建带特征和标签的数据集

    Parameters
    ----------
    df : pd.DataFrame
        原始行情数据 (至少包含 ['open','high','low','close','volume'])
    use_soft : bool
        是否打软标签
    use_triple_barrier : bool
        是否打三重边界标签
    horizon : int
        软标签 horizon
    pt_sl : tuple
        三重边界止盈止损倍数
    max_holding : int
        三重边界最大持仓周期

    Returns
    -------
    X : pd.DataFrame
        特征矩阵
    y : pd.Series or pd.DataFrame
        标签
    """
    # Step 1: 计算基础特征
    df_feat = compute_basic_features(df)

    # Step 2: 添加标签
    df_feat = add_labels(df_feat,
                         use_soft=use_soft,
                         use_triple_barrier=use_triple_barrier,
                         horizon=horizon,
                         pt_sl=pt_sl,
                         max_holding=max_holding)

    # Step 3: 去掉缺失值
    df_feat = df_feat.dropna()

    # Step 4: 拆分 X / y
    feature_cols = [c for c in df_feat.columns
                    if c not in ["soft_label", "future_return",
                                 "triple_barrier_label"]]

    label_cols = []
    if use_soft:
        label_cols += ["soft_label", "future_return"]
    if use_triple_barrier:
        label_cols += ["triple_barrier_label"]

    X = df_feat[feature_cols]
    y = df_feat[label_cols] if len(label_cols) > 1 else df_feat[label_cols[0]]

    return X, y
```

---

这样就能做到：

```python
from features.build_features import build_dataset

# 假设 df 是你的 K 线 DataFrame
X, y = build_dataset(df,
                     use_soft=True,
                     use_triple_barrier=True,
                     horizon=30,
                     pt_sl=(1,1),
                     max_holding=20)

print(X.head())
print(y.head())
```


