# 写一个 `utils/labeling.py`，把 **三重边界打标 (triple barrier)** 实现出来，这样数据集就能马上跑

> **三重边界打标 (Triple Barrier Method, TBM)**，其实就是经典的 **López de Prado《Advances in Financial Machine Learning》** 中提出的标注方式。

它的核心思想：

1. 给定一个观测点 $t_0$，我们向前看一段 **最大持仓期 (horizon)**。
2. 在这段时间里设置三条“障碍”：

   * **上界**：价格向上突破一个阈值 → 记为 **+1（买方胜利）**
   * **下界**：价格向下突破一个阈值 → 记为 **-1（卖方胜利）**
   * **时间界**：如果到期还没有突破上下界 → 记为 **0（中性）**
3. 这样标记出来的标签比单纯“未来 N 步涨跌”更健壮，因为它考虑了 **止盈/止损** 的动态过程。

---

### 🔄 关于你问的区别：

* 在你提到的 **“Backtrader + Optuna + sklearn + ccxt 的端到端脚手架”** 里，我记得你用的是 **回测策略里的止盈止损逻辑** 去打标签（实际上等价于 triple barrier，但通常是通过交易回测来实现）。
* 在这里写的 **utils/labeling.py** 脚手架，是直接在 **数据预处理阶段** 就用 triple barrier 算法打标签，不依赖回测引擎。

📌 总结：**效果上基本等价**（都是三重边界思想），区别在于：

* **回测版**：依赖策略执行（比较真实，但计算更慢，受策略参数影响）
* **数据集版**：独立的预处理工具（快，方便做大规模 ML 训练）

---

## 🚀 utils/labeling.py 脚手架

```python
# utils/labeling.py
import pandas as pd
import numpy as np

def triple_barrier_labeling(df, horizon=30, up_pct=0.01, down_pct=0.01, price_col="close"):
    """
    三重边界打标 (Triple Barrier Method)
    
    参数
    ----
    df : pd.DataFrame
        必须包含 'close' 列
    horizon : int
        向前看的时间窗口 (单位 = 行数，例如1m数据里的30 = 30分钟)
    up_pct : float
        上边界阈值 (相对当前价格的比例，例如 0.01 = +1%)
    down_pct : float
        下边界阈值 (相对当前价格的比例，例如 0.01 = -1%)
    price_col : str
        使用的价格列 (默认收盘价)

    返回
    ----
    labels : pd.Series
        值 ∈ {-1, 0, 1}
    """
    prices = df[price_col].values
    n = len(prices)
    labels = np.zeros(n)

    for i in range(n):
        start_price = prices[i]
        if np.isnan(start_price):
            labels[i] = np.nan
            continue

        upper = start_price * (1 + up_pct)
        lower = start_price * (1 - down_pct)
        end_idx = min(i + horizon, n - 1)

        # 遍历未来 horizon 内价格
        path = prices[i:end_idx + 1]
        hit_up = np.where(path >= upper)[0]
        hit_down = np.where(path <= lower)[0]

        if len(hit_up) > 0 and (len(hit_down) == 0 or hit_up[0] < hit_down[0]):
            labels[i] = 1   # 先触发上界
        elif len(hit_down) > 0 and (len(hit_up) == 0 or hit_down[0] < hit_up[0]):
            labels[i] = -1  # 先触发下界
        else:
            labels[i] = 0   # 时间到期仍未突破

    return pd.Series(labels, index=df.index, name="label")
```

---

### 📊 使用示例

```python
if __name__ == "__main__":
    # 假设有1分钟行情
    df = pd.DataFrame({
        "close": np.linspace(100, 120, 200) + np.random.randn(200)
    }, index=pd.date_range("2024-01-01", periods=200, freq="T"))

    labels = triple_barrier_labeling(df, horizon=30, up_pct=0.01, down_pct=0.01)
    df["label"] = labels
    print(df.head(50))
```

输出的 `label`：

* `1` → 未来30分钟内先涨超过 +1%
* `-1` → 未来30分钟内先跌超过 -1%
* `0` → 到30分钟都没突破

---

这样，你就能和 `train_dataset.py` 无缝衔接了 🎯

* `X` = 各周期特征
* `y` = triple barrier 打标标签


---

## 1. `utils/labeling.py` vs `features/labeling.py`

* **`utils/labeling.py`**
  👉 定位在**工具库**，主要放置**可复用的、和业务无关的打标函数**。比如：

  * `triple_barrier_labeling()`
  * `soft_labeling()`
  * `binance_to_utc()`（时区校准之类的小函数）

  它更偏向于**通用库**，可以在 `features/`、`strategy/` 等地方被 import 使用。

* **`features/labeling.py`**
  👉 定位在**特征工程流水线**，主要负责**把标签和特征矩阵拼接**。比如：

  * 读取清洗后的数据
  * 调用 `utils.labeling.triple_barrier_labeling()` 来生成标签
  * 和 `indicators.py` 里的特征合并，输出一张 **训练用的特征表**

  它更像是**项目级 glue code**，而不是底层通用函数。

➡️ 所以 **不会混淆**，因为：

* `utils/labeling.py` → 算法函数库
* `features/labeling.py` → 实际落地在特征流水线的执行脚本

举个类比：
就像 `numpy.linalg` 提供矩阵分解算法，但 `sklearn.preprocessing.StandardScaler` 会调用它来完成归一化。

---

## 2. 回答你的关键疑问

* “向前看一段最大持仓期” → **未来行情**（在打标签时是“偷看未来”，但在训练/预测时不会泄漏，因为特征只来自过去）
* “未来30分钟内先涨超过+1%” → 也是**未来数据**，标签阶段偷看未来是允许的
* “软版标签 vs 三重边界”

  * **三重边界**：纯分类，结果是 {+1, 0, -1}
  * **软标签**：同时提供分类（方向）+ 回归（幅度），更灵活
  * 软标签**不是替代**，而是**超集**，可以兼容三重边界



