"""
Optuna优化目标函数模块
定义各种优化目标函数
"""

from __future__ import annotations
import sys
import os
from typing import Optional, Dict, Any, Callable
import pandas as pd
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 可选依赖
try:
    import optuna
except ImportError:
    optuna = None

from strategy.run_backtest import run_backtest
from strategy.ml_filter import MLFilterStrategy
from utils.config import OptimizationConfig
from utils.logger import get_logger

logger = get_logger("main")


def create_backtest_objective(df: pd.DataFrame, strategy_class=None,
                             model_path: Optional[str] = None,
                             optimization_config: Optional[OptimizationConfig] = None) -> Callable:
    """
    创建回测优化目标函数

    Args:
        df: OHLCV数据
        strategy_class: 策略类
        model_path: 模型路径
        optimization_config: 优化配置

    Returns:
        目标函数
    """
    if strategy_class is None:
        strategy_class = MLFilterStrategy

    if optimization_config is None:
        optimization_config = OptimizationConfig()

    def objective(trial: "optuna.trial.Trial") -> float:
        """
        Optuna目标函数

        Args:
            trial: Optuna试验对象

        Returns:
            优化目标值
        """
        try:
            # 建议参数
            params = suggest_parameters(trial, optimization_config)

            # 添加模型路径
            if model_path:
                params['model_path'] = model_path

            # 运行回测
            metrics = run_backtest(df, strategy_class, params)

            # 计算目标分数
            score = calculate_objective_score(metrics, trial)

            # 记录中间结果
            trial.set_user_attr('total_return', metrics['total_return'])
            trial.set_user_attr('sharpe_ratio', metrics['sharpe_ratio'])
            trial.set_user_attr('max_drawdown', metrics['max_drawdown'])
            trial.set_user_attr('total_trades', metrics['total_trades'])
            trial.set_user_attr('win_rate', metrics['win_rate'])

            logger.info(f"Trial {trial.number}: Score={score:.4f}, "
                       f"Return={metrics['total_return']:.2%}, "
                       f"Sharpe={metrics['sharpe_ratio']:.4f}, "
                       f"DD={metrics['max_drawdown']:.2%}")

            return score

        except Exception as e:
            logger.error(f"Trial {trial.number} 失败: {e}")
            return -np.inf  # 返回极小值表示失败

    return objective


def suggest_parameters(trial: "optuna.trial.Trial",
                      config: OptimizationConfig) -> Dict[str, Any]:
    """
    建议优化参数

    Args:
        trial: Optuna试验对象
        config: 优化配置

    Returns:
        参数字典
    """
    # 均线参数
    ma_s = trial.suggest_int("ma_s", *config.ma_s_range)
    ma_l = trial.suggest_int("ma_l", *config.ma_l_range)

    # 确保长期均线大于短期均线
    if ma_l - ma_s < 8:
        ma_l = ma_s + 8

    # 止盈止损参数
    tp_atr = trial.suggest_float("tp_atr", *config.tp_atr_range)
    sl_atr = trial.suggest_float("sl_atr", *config.sl_atr_range)

    # 回踩参数
    delta = trial.suggest_float("delta", *config.delta_range)

    # ML概率阈值
    prob_threshold = trial.suggest_float("prob_threshold", *config.prob_threshold_range)

    # 其他固定参数
    params = {
        'ma_s': ma_s,
        'ma_l': ma_l,
        'atr_n': 14,
        'tp_atr': tp_atr,
        'sl_atr': sl_atr,
        'max_hold_bars': 24,
        'delta': delta,
        'prob_threshold': prob_threshold,
        'commission': 0.0007,
        'cash_start': 10000,
        'risk_perc': 0.003
    }

    return params


def calculate_objective_score(metrics: Dict[str, float],
                            trial: Optional["optuna.trial.Trial"] = None) -> float:
    """
    计算目标分数

    Args:
        metrics: 回测指标
        trial: Optuna试验对象（可选）

    Returns:
        目标分数
    """
    sharpe = metrics.get('sharpe_ratio', 0.0)
    max_dd = metrics.get('max_drawdown', 0.0)
    total_return = metrics.get('total_return', 0.0)
    total_trades = metrics.get('total_trades', 0)
    win_rate = metrics.get('win_rate', 0.0)

    # 基础分数：夏普比率
    score = sharpe

    # 回撤惩罚
    if max_dd > 0:
        score -= 0.5 * max_dd  # 回撤惩罚

    # 收益率奖励
    if total_return > 0:
        score += 0.1 * total_return  # 收益率奖励

    # 交易频率惩罚（避免过度交易）
    if total_trades > 100:
        score -= 0.001 * (total_trades - 100)

    # 胜率奖励
    if win_rate > 0.5:
        score += 0.1 * (win_rate - 0.5)

    return score


def create_ml_objective(X: pd.DataFrame, y: pd.Series) -> Callable:
    """
    创建机器学习模型优化目标函数

    Args:
        X: 特征数据
        y: 标签数据

    Returns:
        ML目标函数
    """
    from sklearn.model_selection import TimeSeriesSplit
    from sklearn.metrics import roc_auc_score
    from models.train import create_model_pipeline

    def objective(trial: "optuna.trial.Trial") -> float:
        """
        ML模型优化目标函数
        """
        try:
            # 建议模型参数
            model_type = trial.suggest_categorical("model_type",
                                                  ["GradientBoostingClassifier",
                                                   "RandomForestClassifier",
                                                   "LogisticRegression"])

            model_params = {}

            if model_type == "GradientBoostingClassifier":
                model_params = {
                    'n_estimators': trial.suggest_int('n_estimators', 50, 300),
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                    'max_depth': trial.suggest_int('max_depth', 3, 10)
                }
            elif model_type == "RandomForestClassifier":
                model_params = {
                    'n_estimators': trial.suggest_int('n_estimators', 50, 300),
                    'max_depth': trial.suggest_int('max_depth', 5, 20),
                    'min_samples_split': trial.suggest_int('min_samples_split', 2, 20)
                }
            elif model_type == "LogisticRegression":
                model_params = {
                    'C': trial.suggest_float('C', 0.01, 100.0, log=True),
                    'max_iter': trial.suggest_int('max_iter', 1000, 5000)
                }

            # 创建模型
            model = create_model_pipeline(model_type, model_params)

            # 时间序列交叉验证
            tscv = TimeSeriesSplit(n_splits=3)
            scores = []

            y_bin = (y > 0).astype(int)

            for train_idx, val_idx in tscv.split(X):
                X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
                y_train, y_val = y_bin.iloc[train_idx], y_bin.iloc[val_idx]

                model.fit(X_train, y_train)
                y_pred_proba = model.predict_proba(X_val)[:, 1]

                score = roc_auc_score(y_val, y_pred_proba)
                scores.append(score)

            mean_score = np.mean(scores)

            logger.info(f"ML Trial {trial.number}: AUC={mean_score:.4f}, "
                       f"Model={model_type}")

            return mean_score

        except Exception as e:
            logger.error(f"ML Trial {trial.number} 失败: {e}")
            return 0.0

    return objective


def create_multi_objective(df: pd.DataFrame, strategy_class=None,
                          model_path: Optional[str] = None) -> Callable:
    """
    创建多目标优化函数

    Args:
        df: OHLCV数据
        strategy_class: 策略类
        model_path: 模型路径

    Returns:
        多目标函数
    """
    def objective(trial: "optuna.trial.Trial") -> tuple:
        """
        多目标优化函数
        返回多个目标值
        """
        try:
            config = OptimizationConfig()
            params = suggest_parameters(trial, config)

            if model_path:
                params['model_path'] = model_path

            metrics = run_backtest(df, strategy_class, params)

            # 返回多个目标：最大化夏普比率，最小化回撤
            sharpe = metrics.get('sharpe_ratio', 0.0)
            max_dd = metrics.get('max_drawdown', 0.0)

            return sharpe, -max_dd  # 负号表示最小化回撤

        except Exception as e:
            logger.error(f"Multi-objective Trial {trial.number} 失败: {e}")
            return -np.inf, np.inf

    return objective

