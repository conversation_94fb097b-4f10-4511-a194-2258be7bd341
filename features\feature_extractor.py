"""
统一特征提取器
确保训练和回测使用完全相同的特征集
"""

from __future__ import annotations
import numpy as np
import pandas as pd
from typing import Optional, Dict, List, Callable, Any
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.helpers import clean_data
from utils.config import SignalParams
from config.feature_config import FeatureConfig, FeatureConfigTemplates


class FeatureExtractor:
    """
    统一特征提取器
    确保训练和回测使用完全相同的特征集
    """
    
    def __init__(self, signal_params: Optional[SignalParams] = None,
                 feature_config: Optional[FeatureConfig] = None):
        """
        初始化特征提取器

        Args:
            signal_params: 信号参数配置
            feature_config: 特征配置
        """
        self.signal_params = signal_params or SignalParams()
        self.feature_config = feature_config or FeatureConfigTemplates.standard()
        self.custom_features: Dict[str, Callable] = {}
        self.feature_order: List[str] = []
        self._initialize_feature_order()
        self._load_custom_features()
    
    def _initialize_feature_order(self):
        """初始化特征顺序（根据配置）"""
        self.feature_order = []

        # 基础特征 (26个)
        if self.feature_config.use_basic_features:
            basic_features = [
                "ma_diff", "ma_ratio", "ma_slope_s", "ma_slope_l",
                "atr", "atr_ratio",
                "bb_width", "bb_percent",
                "ret_1", "ret_5", "ret_10",
                "rsi", "rsi_oversold", "rsi_overbought",
                "macd", "macd_signal", "macd_histogram",
                "volume_ratio", "dir",
                "hour", "dow", "month",
                "price_position_20", "price_position_50",
                "volatility_5", "volatility_20"
            ]
            self.feature_order.extend(basic_features)

        # 高级特征 (7个)
        if self.feature_config.use_advanced_features:
            advanced_features = [
                "price_channel_20", "trend_strength", "momentum_divergence",
                "volume_price_trend", "volatility_breakout",
                "price_acceleration", "ma_distance_normalized"
            ]
            self.feature_order.extend(advanced_features)

        # 自定义特征将在 _load_custom_features 中添加
    
    def _load_custom_features(self):
        """加载配置中的自定义特征"""
        custom_feature_functions = self.feature_config.get_custom_feature_functions()
        for name, func in custom_feature_functions.items():
            self.custom_features[name] = func
            if name not in self.feature_order:
                self.feature_order.append(name)

    def add_custom_feature(self, name: str, func: Callable[[pd.DataFrame], pd.Series]):
        """
        添加用户自定义特征

        Args:
            name: 特征名称
            func: 特征计算函数，接收DataFrame返回Series
        """
        self.custom_features[name] = func
        if name not in self.feature_order:
            self.feature_order.append(name)
    
    def extract_features_from_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        从DataFrame提取特征（用于训练）
        
        Args:
            df: 包含指标的DataFrame
            
        Returns:
            特征DataFrame
        """
        features = pd.DataFrame(index=df.index)
        
        # 基础特征
        if self.feature_config.use_basic_features:
            features = self._extract_basic_features(features, df)

        # 高级特征
        if self.feature_config.use_advanced_features:
            features = self._extract_advanced_features(features, df)

        # 用户自定义特征
        if self.custom_features:
            features = self._extract_custom_features(features, df)
        
        # 清理数据
        features = clean_data(features)
        
        # 按照预定义顺序重新排列特征
        available_features = [f for f in self.feature_order if f in features.columns]
        features = features[available_features]
        
        return features
    
    def extract_features_from_backtrader(self, strategy) -> np.ndarray:
        """
        从Backtrader策略对象提取特征（用于回测）

        Args:
            strategy: Backtrader策略对象

        Returns:
            特征数组
        """
        # 首先构建一个临时DataFrame来使用相同的计算逻辑
        temp_df = self._build_temp_dataframe_from_strategy(strategy)

        # 使用DataFrame方法提取特征（对整个临时DataFrame）
        temp_features = pd.DataFrame(index=temp_df.index)

        # 基础特征
        if self.feature_config.use_basic_features:
            temp_features = self._extract_basic_features(temp_features, temp_df)

        # 高级特征
        if self.feature_config.use_advanced_features:
            temp_features = self._extract_advanced_features(temp_features, temp_df)

        # 用户自定义特征
        if self.custom_features:
            temp_features = self._extract_custom_features(temp_features, temp_df)

        # 按照预定义顺序转换为数组
        feature_values = []
        for feature_name in self.feature_order:
            if feature_name in temp_features.columns:
                value = temp_features[feature_name].iloc[-1]
                feature_values.append(float(value) if not pd.isna(value) else 0.0)
            else:
                feature_values.append(0.0)

        arr = np.array(feature_values, dtype=float).reshape(1, -1)
        return arr

    def _build_temp_dataframe_from_strategy(self, strategy) -> pd.DataFrame:
        """
        从Backtrader策略构建临时DataFrame

        Args:
            strategy: Backtrader策略对象

        Returns:
            临时DataFrame
        """
        # 获取足够的历史数据用于计算技术指标
        lookback = 100  # 足够计算各种指标

        # 构建OHLCV数据
        data_length = len(strategy.data_close)
        start_idx = max(0, data_length - lookback)

        # 提取价格数据
        closes = []
        opens = []
        highs = []
        lows = []
        volumes = []

        for i in range(start_idx, data_length):
            idx_offset = i - data_length
            closes.append(float(strategy.data_close[idx_offset]))

            # 如果策略有OHLV数据，使用它们；否则使用close价格近似
            if hasattr(strategy, 'data_open'):
                opens.append(float(strategy.data_open[idx_offset]))
            else:
                opens.append(closes[-1])

            if hasattr(strategy, 'data_high'):
                highs.append(float(strategy.data_high[idx_offset]))
            else:
                highs.append(closes[-1])

            if hasattr(strategy, 'data_low'):
                lows.append(float(strategy.data_low[idx_offset]))
            else:
                lows.append(closes[-1])

            if hasattr(strategy, 'data_volume'):
                volumes.append(float(strategy.data_volume[idx_offset]))
            else:
                volumes.append(1000.0)  # 默认成交量

        # 创建时间索引（使用固定的基准时间确保一致性）
        base_time = pd.Timestamp('2023-01-01 00:00:00')
        dates = pd.date_range(
            start=base_time,
            periods=len(closes),
            freq='h'
        )

        # 构建DataFrame
        df = pd.DataFrame({
            'open': opens,
            'high': highs,
            'low': lows,
            'close': closes,
            'volume': volumes,
        }, index=dates)

        # 添加已有的技术指标
        if hasattr(strategy, 'ema_s'):
            ema_s_values = []
            ema_l_values = []
            atr_values = []

            for i in range(start_idx, data_length):
                idx_offset = i - data_length
                ema_s_values.append(float(strategy.ema_s[idx_offset]))
                ema_l_values.append(float(strategy.ema_l[idx_offset]))
                atr_values.append(float(strategy.atr[idx_offset]))

            df['ema_s'] = ema_s_values
            df['ema_l'] = ema_l_values
            df['atr'] = atr_values

            # 计算方向特征
            df['dir'] = np.where(df['ema_s'] > df['ema_l'], 1,
                               np.where(df['ema_s'] < df['ema_l'], -1, 0))

        # 添加其他必要的技术指标
        from features.indicators import add_all_indicators
        df = add_all_indicators(df, self.signal_params)

        return df
    
    def _extract_basic_features(self, features: pd.DataFrame, df: pd.DataFrame) -> pd.DataFrame:
        """提取基础特征（DataFrame版本）"""
        # 均线特征
        features["ma_diff"] = df["ema_s"] - df["ema_l"]
        features["ma_ratio"] = df["ema_s"] / (df["ema_l"] + 1e-12) - 1
        features["ma_slope_s"] = df["ema_s"].diff()
        features["ma_slope_l"] = df["ema_l"].diff()
        
        # ATR特征
        features["atr"] = df["atr"]
        features["atr_ratio"] = df["atr"] / df["close"]
        
        # 布林带特征
        if "bb_width" in df.columns:
            features["bb_width"] = df["bb_width"]
            features["bb_percent"] = df["bb_percent"]
        else:
            features["bb_width"] = (df["close"].rolling(20).std() * 2).fillna(method="bfill")
            features["bb_percent"] = 0.5
        
        # 价格动量特征
        features["ret_1"] = df["close"].pct_change().fillna(0)
        features["ret_5"] = df["close"].pct_change(5).fillna(0)
        features["ret_10"] = df["close"].pct_change(10).fillna(0)
        
        # RSI特征
        if "rsi" in df.columns:
            features["rsi"] = df["rsi"]
            features["rsi_oversold"] = (df["rsi"] < 30).astype(int)
            features["rsi_overbought"] = (df["rsi"] > 70).astype(int)
        else:
            features["rsi"] = 50.0
            features["rsi_oversold"] = 0.0
            features["rsi_overbought"] = 0.0
        
        # MACD特征
        if "macd" in df.columns:
            features["macd"] = df["macd"]
            features["macd_signal"] = df["macd_signal"]
            features["macd_histogram"] = df["macd_histogram"]
        else:
            features["macd"] = 0.0
            features["macd_signal"] = 0.0
            features["macd_histogram"] = 0.0
        
        # 成交量特征
        if "volume_ratio" in df.columns:
            features["volume_ratio"] = df["volume_ratio"]
        else:
            features["volume_ratio"] = 1.0
        
        # 方向特征
        features["dir"] = df["dir"].fillna(0)
        
        # 时间特征
        features["hour"] = df.index.tz_convert("UTC").hour if df.index.tz is not None else df.index.hour
        features["dow"] = df.index.dayofweek
        features["month"] = df.index.month
        
        # 价格位置特征
        features["price_position_20"] = df["close"] / df["close"].rolling(20).max()
        features["price_position_50"] = df["close"] / df["close"].rolling(50).max()
        
        # 波动率特征
        features["volatility_5"] = df["close"].pct_change().rolling(5).std()
        features["volatility_20"] = df["close"].pct_change().rolling(20).std()
        
        return features
    
    def _extract_advanced_features(self, features: pd.DataFrame, df: pd.DataFrame) -> pd.DataFrame:
        """提取高级特征（DataFrame版本）"""
        # 价格通道特征
        features["price_channel_20"] = (df["close"] - df["close"].rolling(20).min()) / \
                                      (df["close"].rolling(20).max() - df["close"].rolling(20).min() + 1e-12)
        
        # 趋势强度特征
        features["trend_strength"] = abs(features["ma_diff"]) / (features["atr"] + 1e-12)
        
        # 动量发散特征
        if "macd_histogram" in features.columns:
            features["momentum_divergence"] = features["macd_histogram"].diff()
        else:
            features["momentum_divergence"] = 0.0
        
        # 成交量价格趋势
        if "volume_ratio" in features.columns:
            features["volume_price_trend"] = features["volume_ratio"] * features["ret_1"]
        else:
            features["volume_price_trend"] = 0.0
        
        # 波动率突破特征
        if "volatility_20" in features.columns:
            vol_ma = features["volatility_20"].rolling(20).mean()
            features["volatility_breakout"] = features["volatility_20"] / (vol_ma + 1e-12)
        else:
            features["volatility_breakout"] = 1.0
        
        # 价格加速度
        features["price_acceleration"] = features["ret_1"].diff()
        
        # 均线距离标准化
        features["ma_distance_normalized"] = features["ma_diff"] / (features["atr"] + 1e-12)
        
        return features
    
    def _extract_custom_features(self, features: pd.DataFrame, df: pd.DataFrame) -> pd.DataFrame:
        """提取用户自定义特征（DataFrame版本）"""
        for name, func in self.custom_features.items():
            try:
                features[name] = func(df)
            except Exception as e:
                print(f"Warning: Failed to compute custom feature {name}: {e}")
                features[name] = 0.0
        return features
    
    def get_feature_count(self) -> int:
        """获取特征总数"""
        return len(self.feature_order)
    
    def get_feature_names(self) -> List[str]:
        """获取特征名称列表"""
        return self.feature_order.copy()


