"""
统一特征提取器
确保训练和回测使用完全相同的特征集
"""

from __future__ import annotations
import numpy as np
import pandas as pd
from typing import Optional, Dict, List, Callable, Any
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.helpers import clean_data
from utils.config import SignalParams
from config.feature_config import FeatureConfig, FeatureConfigTemplates


class FeatureExtractor:
    """
    统一特征提取器
    确保训练和回测使用完全相同的特征集
    """
    
    def __init__(self, signal_params: Optional[SignalParams] = None,
                 feature_config: Optional[FeatureConfig] = None):
        """
        初始化特征提取器

        Args:
            signal_params: 信号参数配置
            feature_config: 特征配置
        """
        self.signal_params = signal_params or SignalParams()
        self.feature_config = feature_config or FeatureConfigTemplates.standard()
        self.custom_features: Dict[str, Callable] = {}
        self.feature_order: List[str] = []
        self._initialize_feature_order()
        self._load_custom_features()
    
    def _initialize_feature_order(self):
        """初始化特征顺序（根据配置）"""
        self.feature_order = []

        # 基础特征 (26个)
        if self.feature_config.use_basic_features:
            basic_features = [
                "ma_diff", "ma_ratio", "ma_slope_s", "ma_slope_l",
                "atr", "atr_ratio",
                "bb_width", "bb_percent",
                "ret_1", "ret_5", "ret_10",
                "rsi", "rsi_oversold", "rsi_overbought",
                "macd", "macd_signal", "macd_histogram",
                "volume_ratio", "dir",
                "hour", "dow", "month",
                "price_position_20", "price_position_50",
                "volatility_5", "volatility_20"
            ]
            self.feature_order.extend(basic_features)

        # 高级特征 (7个)
        if self.feature_config.use_advanced_features:
            advanced_features = [
                "price_channel_20", "trend_strength", "momentum_divergence",
                "volume_price_trend", "volatility_breakout",
                "price_acceleration", "ma_distance_normalized"
            ]
            self.feature_order.extend(advanced_features)

        # 自定义特征将在 _load_custom_features 中添加
    
    def _load_custom_features(self):
        """加载配置中的自定义特征"""
        custom_feature_functions = self.feature_config.get_custom_feature_functions()
        for name, func in custom_feature_functions.items():
            self.custom_features[name] = func
            if name not in self.feature_order:
                self.feature_order.append(name)

    def add_custom_feature(self, name: str, func: Callable[[pd.DataFrame], pd.Series]):
        """
        添加用户自定义特征

        Args:
            name: 特征名称
            func: 特征计算函数，接收DataFrame返回Series
        """
        self.custom_features[name] = func
        if name not in self.feature_order:
            self.feature_order.append(name)
    
    def extract_features_from_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        从DataFrame提取特征（用于训练）
        
        Args:
            df: 包含指标的DataFrame
            
        Returns:
            特征DataFrame
        """
        features = pd.DataFrame(index=df.index)
        
        # 基础特征
        if self.feature_config.use_basic_features:
            features = self._extract_basic_features(features, df)

        # 高级特征
        if self.feature_config.use_advanced_features:
            features = self._extract_advanced_features(features, df)

        # 用户自定义特征
        if self.custom_features:
            features = self._extract_custom_features(features, df)
        
        # 清理数据
        features = clean_data(features)
        
        # 按照预定义顺序重新排列特征
        available_features = [f for f in self.feature_order if f in features.columns]
        features = features[available_features]
        
        return features
    
    def extract_features_from_backtrader(self, strategy) -> np.ndarray:
        """
        从Backtrader策略对象提取特征（用于回测）
        
        Args:
            strategy: Backtrader策略对象
            
        Returns:
            特征数组
        """
        features = {}
        
        # 获取当前指标值
        ma_s_val = float(strategy.ema_s[0])
        ma_l_val = float(strategy.ema_l[0])
        atr_val = float(strategy.atr[0]) + 1e-12
        close = float(strategy.data_close[0])
        
        # 基础特征
        if self.feature_config.use_basic_features:
            features.update(self._extract_basic_features_bt(strategy, ma_s_val, ma_l_val, atr_val, close))

        # 高级特征
        if self.feature_config.use_advanced_features:
            features.update(self._extract_advanced_features_bt(strategy, features, close))

        # 用户自定义特征
        if self.custom_features:
            features.update(self._extract_custom_features_bt(strategy, features))
        
        # 按照预定义顺序转换为数组
        arr = np.array([features.get(k, 0.0) for k in self.feature_order], dtype=float).reshape(1, -1)
        return arr
    
    def _extract_basic_features(self, features: pd.DataFrame, df: pd.DataFrame) -> pd.DataFrame:
        """提取基础特征（DataFrame版本）"""
        # 均线特征
        features["ma_diff"] = df["ema_s"] - df["ema_l"]
        features["ma_ratio"] = df["ema_s"] / (df["ema_l"] + 1e-12) - 1
        features["ma_slope_s"] = df["ema_s"].diff()
        features["ma_slope_l"] = df["ema_l"].diff()
        
        # ATR特征
        features["atr"] = df["atr"]
        features["atr_ratio"] = df["atr"] / df["close"]
        
        # 布林带特征
        if "bb_width" in df.columns:
            features["bb_width"] = df["bb_width"]
            features["bb_percent"] = df["bb_percent"]
        else:
            features["bb_width"] = (df["close"].rolling(20).std() * 2).fillna(method="bfill")
            features["bb_percent"] = 0.5
        
        # 价格动量特征
        features["ret_1"] = df["close"].pct_change().fillna(0)
        features["ret_5"] = df["close"].pct_change(5).fillna(0)
        features["ret_10"] = df["close"].pct_change(10).fillna(0)
        
        # RSI特征
        if "rsi" in df.columns:
            features["rsi"] = df["rsi"]
            features["rsi_oversold"] = (df["rsi"] < 30).astype(int)
            features["rsi_overbought"] = (df["rsi"] > 70).astype(int)
        else:
            features["rsi"] = 50.0
            features["rsi_oversold"] = 0.0
            features["rsi_overbought"] = 0.0
        
        # MACD特征
        if "macd" in df.columns:
            features["macd"] = df["macd"]
            features["macd_signal"] = df["macd_signal"]
            features["macd_histogram"] = df["macd_histogram"]
        else:
            features["macd"] = 0.0
            features["macd_signal"] = 0.0
            features["macd_histogram"] = 0.0
        
        # 成交量特征
        if "volume_ratio" in df.columns:
            features["volume_ratio"] = df["volume_ratio"]
        else:
            features["volume_ratio"] = 1.0
        
        # 方向特征
        features["dir"] = df["dir"].fillna(0)
        
        # 时间特征
        features["hour"] = df.index.tz_convert("UTC").hour if df.index.tz is not None else df.index.hour
        features["dow"] = df.index.dayofweek
        features["month"] = df.index.month
        
        # 价格位置特征
        features["price_position_20"] = df["close"] / df["close"].rolling(20).max()
        features["price_position_50"] = df["close"] / df["close"].rolling(50).max()
        
        # 波动率特征
        features["volatility_5"] = df["close"].pct_change().rolling(5).std()
        features["volatility_20"] = df["close"].pct_change().rolling(20).std()
        
        return features
    
    def _extract_advanced_features(self, features: pd.DataFrame, df: pd.DataFrame) -> pd.DataFrame:
        """提取高级特征（DataFrame版本）"""
        # 价格通道特征
        features["price_channel_20"] = (df["close"] - df["close"].rolling(20).min()) / \
                                      (df["close"].rolling(20).max() - df["close"].rolling(20).min() + 1e-12)
        
        # 趋势强度特征
        features["trend_strength"] = abs(features["ma_diff"]) / (features["atr"] + 1e-12)
        
        # 动量发散特征
        if "macd_histogram" in features.columns:
            features["momentum_divergence"] = features["macd_histogram"].diff()
        else:
            features["momentum_divergence"] = 0.0
        
        # 成交量价格趋势
        if "volume_ratio" in features.columns:
            features["volume_price_trend"] = features["volume_ratio"] * features["ret_1"]
        else:
            features["volume_price_trend"] = 0.0
        
        # 波动率突破特征
        if "volatility_20" in features.columns:
            vol_ma = features["volatility_20"].rolling(20).mean()
            features["volatility_breakout"] = features["volatility_20"] / (vol_ma + 1e-12)
        else:
            features["volatility_breakout"] = 1.0
        
        # 价格加速度
        features["price_acceleration"] = features["ret_1"].diff()
        
        # 均线距离标准化
        features["ma_distance_normalized"] = features["ma_diff"] / (features["atr"] + 1e-12)
        
        return features
    
    def _extract_custom_features(self, features: pd.DataFrame, df: pd.DataFrame) -> pd.DataFrame:
        """提取用户自定义特征（DataFrame版本）"""
        for name, func in self.custom_features.items():
            try:
                features[name] = func(df)
            except Exception as e:
                print(f"Warning: Failed to compute custom feature {name}: {e}")
                features[name] = 0.0
        return features
    
    def get_feature_count(self) -> int:
        """获取特征总数"""
        return len(self.feature_order)
    
    def get_feature_names(self) -> List[str]:
        """获取特征名称列表"""
        return self.feature_order.copy()

    def _extract_basic_features_bt(self, strategy, ma_s_val: float, ma_l_val: float,
                                  atr_val: float, close: float) -> Dict[str, float]:
        """提取基础特征（Backtrader版本）"""
        features = {}

        # 均线特征
        features["ma_diff"] = ma_s_val - ma_l_val
        features["ma_ratio"] = ma_s_val / (ma_l_val + 1e-12) - 1
        features["ma_slope_s"] = self._get_slope(strategy.ema_s)
        features["ma_slope_l"] = self._get_slope(strategy.ema_l)

        # ATR特征
        features["atr"] = atr_val
        features["atr_ratio"] = atr_val / close

        # 布林带特征
        bb_width = self._get_bb_width(strategy)
        features["bb_width"] = bb_width
        features["bb_percent"] = self._get_bb_percent(close, bb_width)

        # 价格动量特征
        features["ret_1"] = self._get_return(strategy, 1)
        features["ret_5"] = self._get_return(strategy, 5)
        features["ret_10"] = self._get_return(strategy, 10)

        # RSI特征
        rsi_val = self._get_rsi_approx(strategy)
        features["rsi"] = rsi_val
        features["rsi_oversold"] = 1.0 if rsi_val < 30 else 0.0
        features["rsi_overbought"] = 1.0 if rsi_val > 70 else 0.0

        # MACD特征
        macd_val = self._get_macd_approx(strategy)
        features["macd"] = macd_val
        features["macd_signal"] = macd_val * 0.9  # 简化的信号线
        features["macd_histogram"] = macd_val * 0.1  # 简化的柱状图

        # 成交量特征
        features["volume_ratio"] = 1.0  # 默认值

        # 方向特征
        features["dir"] = 1.0 if (ma_s_val - ma_l_val) > 0 else (-1.0 if (ma_s_val - ma_l_val) < 0 else 0.0)

        # 时间特征
        features["hour"] = 12.0  # 默认中午
        features["dow"] = 2.0    # 默认周二
        features["month"] = 6.0  # 默认6月

        # 价格位置特征
        features["price_position_20"] = self._get_price_position(strategy, 20)
        features["price_position_50"] = self._get_price_position(strategy, 50)

        # 波动率特征
        features["volatility_5"] = self._get_volatility(strategy, 5)
        features["volatility_20"] = self._get_volatility(strategy, 20)

        return features

    def _extract_advanced_features_bt(self, strategy, basic_features: Dict[str, float],
                                     close: float) -> Dict[str, float]:
        """提取高级特征（Backtrader版本）"""
        features = {}

        # 价格通道特征
        features["price_channel_20"] = self._get_price_channel(strategy, 20)

        # 趋势强度特征
        features["trend_strength"] = abs(basic_features["ma_diff"]) / (basic_features["atr"] + 1e-12)

        # 动量发散特征
        features["momentum_divergence"] = basic_features["macd_histogram"] * 0.1  # 简化计算

        # 成交量价格趋势
        features["volume_price_trend"] = basic_features["volume_ratio"] * basic_features["ret_1"]

        # 波动率突破特征
        vol_20 = basic_features["volatility_20"]
        features["volatility_breakout"] = vol_20 / (vol_20 * 0.9 + 1e-12)  # 简化计算

        # 价格加速度
        features["price_acceleration"] = basic_features["ret_1"] * 0.1  # 简化计算

        # 均线距离标准化
        features["ma_distance_normalized"] = basic_features["ma_diff"] / (basic_features["atr"] + 1e-12)

        return features

    def _extract_custom_features_bt(self, strategy, features: Dict[str, float]) -> Dict[str, float]:
        """提取用户自定义特征（Backtrader版本）"""
        custom_features = {}
        for name, func in self.custom_features.items():
            try:
                # 用户自定义函数需要适配Backtrader接口
                # 这里提供一个简单的默认实现
                custom_features[name] = 0.0
            except Exception as e:
                print(f"Warning: Failed to compute custom feature {name}: {e}")
                custom_features[name] = 0.0
        return custom_features

    # Backtrader辅助方法
    def _get_slope(self, indicator) -> float:
        """获取指标斜率"""
        try:
            if len(indicator) > 1:
                return float(indicator[0] - indicator[-1])
            else:
                return 0.0
        except:
            return 0.0

    def _get_bb_width(self, strategy) -> float:
        """获取布林带宽度（近似）"""
        try:
            closes = [float(strategy.data_close[-i]) for i in range(min(20, len(strategy.data_close)))]
            if len(closes) > 1:
                return 2.0 * float(np.std(closes))
            else:
                return 0.0
        except:
            return 0.0

    def _get_bb_percent(self, close: float, bb_width: float) -> float:
        """获取布林带百分比位置"""
        try:
            return 0.5  # 简化计算
        except:
            return 0.5

    def _get_return(self, strategy, periods: int) -> float:
        """获取收益率"""
        try:
            if len(strategy.data_close) > periods:
                current = float(strategy.data_close[0])
                past = float(strategy.data_close[-periods])
                return current / past - 1.0
            else:
                return 0.0
        except:
            return 0.0

    def _get_rsi_approx(self, strategy) -> float:
        """获取RSI近似值"""
        try:
            recent_returns = []
            for i in range(min(14, len(strategy.data_close))):
                if i + 1 < len(strategy.data_close):
                    ret = float(strategy.data_close[-i] / strategy.data_close[-i-1] - 1.0)
                    recent_returns.append(ret)

            if len(recent_returns) > 0:
                gains = [r for r in recent_returns if r > 0]
                losses = [-r for r in recent_returns if r < 0]

                avg_gain = np.mean(gains) if gains else 0.0
                avg_loss = np.mean(losses) if losses else 0.0

                if avg_loss == 0:
                    return 100.0

                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
                return rsi
            else:
                return 50.0
        except:
            return 50.0

    def _get_macd_approx(self, strategy) -> float:
        """获取MACD近似值"""
        try:
            return float(strategy.ema_s[0] - strategy.ema_l[0])
        except:
            return 0.0

    def _get_price_position(self, strategy, periods: int) -> float:
        """获取价格在历史最高价中的位置"""
        try:
            current = float(strategy.data_close[0])
            max_prices = []
            for i in range(min(periods, len(strategy.data_close))):
                max_prices.append(float(strategy.data_close[-i]))

            if max_prices:
                max_price = max(max_prices)
                return current / max_price if max_price > 0 else 1.0
            else:
                return 1.0
        except:
            return 1.0

    def _get_volatility(self, strategy, periods: int) -> float:
        """获取波动率"""
        try:
            returns = []
            for i in range(min(periods, len(strategy.data_close) - 1)):
                if i + 1 < len(strategy.data_close):
                    ret = float(strategy.data_close[-i] / strategy.data_close[-i-1] - 1.0)
                    returns.append(ret)

            if len(returns) > 1:
                return float(np.std(returns))
            else:
                return 0.0
        except:
            return 0.0

    def _get_price_channel(self, strategy, periods: int) -> float:
        """获取价格通道位置"""
        try:
            current = float(strategy.data_close[0])
            prices = []
            for i in range(min(periods, len(strategy.data_close))):
                prices.append(float(strategy.data_close[-i]))

            if len(prices) > 1:
                min_price = min(prices)
                max_price = max(prices)
                return (current - min_price) / (max_price - min_price + 1e-12)
            else:
                return 0.5
        except:
            return 0.5
