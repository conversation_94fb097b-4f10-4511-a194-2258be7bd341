扩展一个 **`train_dataset.py`**，把这个特征工程直接打包成 sklearn `X, y` 格式，方便马上拿去喂模型。

**train\_dataset.py** 脚手架的目标，就是把**大周期行情数据（例如1h, 4h 日线）**作为输入，用它生成跨周期的特征工程，然后对齐小周期（例如1m, 5m）的K线，打包成**训练样本集**，喂给监督/强化学习模型。

直观类比：

* **大周期数据** = 天气预报（提供背景趋势/环境）
* **小周期数据** = 实时天气（提供即时信号/波动）
* **数据集** = 同时包含背景和即时信号，供模型学会“什么时候风向变了”

---

## train\_dataset.py 脚手架设计目标

* 输入：历史行情（多个周期，比如 `1m + 1h`）
* 处理：

  1. **加载并对齐多周期数据**（resample/sync）
  2. **调用 features 工程模块**，提取指标
  3. **打标签**（例如未来N分钟价格涨跌、收益率阈值等）
  4. **合并并输出训练集**（特征+标签 CSV/Parquet/feather）
* 输出：干净的机器学习训练数据

---

## 📦 train\_dataset.py 脚手架

```python
# train_dataset.py
import pandas as pd
import numpy as np
from pathlib import Path

from features.feature_engineering import generate_features
from utils.labeling import triple_barrier_labeling

DATA_DIR = Path("./data")
OUTPUT_DIR = Path("./datasets")
OUTPUT_DIR.mkdir(parents=True, exist_ok=True)


def load_data(symbol="BTC/USDT", base_freq="1m", higher_freqs=["1h"]):
    """
    读取多周期数据, base_freq = 小周期, higher_freqs = 大周期
    假设 ./data/symbol_timeframe.csv 已经存在 (由 downloader 脚本生成)
    """
    dfs = {}
    # 读取基础小周期
    base_file = DATA_DIR / f"{symbol.replace('/', '')}_{base_freq}.csv"
    dfs[base_freq] = pd.read_csv(base_file, parse_dates=["datetime"], index_col="datetime")

    # 读取大周期
    for hf in higher_freqs:
        hf_file = DATA_DIR / f"{symbol.replace('/', '')}_{hf}.csv"
        dfs[hf] = pd.read_csv(hf_file, parse_dates=["datetime"], index_col="datetime")

        # 将大周期数据对齐到小周期索引
        dfs[hf] = dfs[hf].reindex(dfs[base_freq].index, method="ffill")

    return dfs


def build_train_dataset(symbol="BTC/USDT", base_freq="1m", higher_freqs=["1h"], horizon=30):
    """
    核心流程: 1. 读取多周期数据 2. 特征提取 3. 打标签 4. 输出训练集
    """
    dfs = load_data(symbol, base_freq, higher_freqs)

    # 提取小周期特征
    features_base = generate_features(dfs[base_freq], prefix=base_freq)

    # 提取大周期特征并合并
    feature_dfs = [features_base]
    for hf in higher_freqs:
        features_hf = generate_features(dfs[hf], prefix=hf)
        feature_dfs.append(features_hf)

    X = pd.concat(feature_dfs, axis=1).dropna()

    # 打标签 (例如未来 horizon 分钟涨跌)
    y = triple_barrier_labeling(dfs[base_freq], horizon=horizon)

    dataset = X.join(y, how="inner").dropna()
    return dataset


if __name__ == "__main__":
    dataset = build_train_dataset(symbol="BTC/USDT", base_freq="1m", higher_freqs=["1h"], horizon=30)

    # 保存数据集
    out_file = OUTPUT_DIR / "btc_usdt_train_1m_with_1h.csv"
    dataset.to_csv(out_file)
    print(f"[INFO] Saved training dataset: {out_file}, shape={dataset.shape}")
```

---

## 🔑 要点解释

1. **多周期数据对齐**：大周期用 `reindex` + `ffill` 映射到小周期时间戳。
2. **特征工程**：调用 `features/feature_engineering.py`（之前写的特征模板，可以扩展）
3. **标签生成**：调用 `utils/labeling.py`，支持 triple-barrier 或二分类（上涨/下跌）。
4. **输出数据集**：标准化 CSV/Parquet，方便 ML pipeline 使用。

---

👉 这样一来，你的训练数据就有：

* **输入特征** = 小周期信号 + 大周期背景趋势
* **输出标签** = “未来 N 分钟价格走势”

可直接喂给 **scikit-learn / XGBoost / LSTM / RL agent** 进行训练。


