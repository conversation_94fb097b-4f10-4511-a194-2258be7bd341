"""
机器学习模型训练模块
支持多种分类器的训练和评估
"""

from __future__ import annotations
import numpy as np
import pandas as pd
from typing import Optional, Dict, Any, Tuple
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sklearn.ensemble import GradientBoostingClassifier, RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV
from sklearn.metrics import roc_auc_score, classification_report
from utils.config import ModelConfig
from utils.logger import get_logger

logger = get_logger("main")


def create_model_pipeline(model_type: str = "GradientBoostingClassifier",
                         model_params: Optional[Dict] = None) -> Pipeline:
    """
    创建模型管道

    Args:
        model_type: 模型类型
        model_params: 模型参数

    Returns:
        sklearn Pipeline
    """
    if model_params is None:
        model_params = {}

    # 选择模型
    if model_type == "GradientBoostingClassifier":
        model = GradientBoostingClassifier(
            random_state=42,
            n_estimators=model_params.get('n_estimators', 100),
            learning_rate=model_params.get('learning_rate', 0.1),
            max_depth=model_params.get('max_depth', 3)
        )
    elif model_type == "RandomForestClassifier":
        model = RandomForestClassifier(
            random_state=42,
            n_estimators=model_params.get('n_estimators', 100),
            max_depth=model_params.get('max_depth', None),
            min_samples_split=model_params.get('min_samples_split', 2)
        )
    elif model_type == "LogisticRegression":
        model = LogisticRegression(
            random_state=42,
            max_iter=model_params.get('max_iter', 1000),
            C=model_params.get('C', 1.0)
        )
    elif model_type == "SVC":
        model = SVC(
            random_state=42,
            probability=True,
            C=model_params.get('C', 1.0),
            kernel=model_params.get('kernel', 'rbf')
        )
    else:
        raise ValueError(f"不支持的模型类型: {model_type}")

    # 创建管道
    pipeline = Pipeline([
        ("scaler", StandardScaler()),
        ("classifier", model)
    ])

    return pipeline


def train_classifier(X: pd.DataFrame, y: pd.Series,
                    config: Optional[ModelConfig] = None) -> Pipeline:
    """
    训练分类器

    Args:
        X: 特征数据
        y: 标签数据
        config: 模型配置

    Returns:
        训练好的模型管道
    """
    if config is None:
        config = ModelConfig()

    # 转换为二分类问题（预测正类概率）
    y_bin = (y > 0).astype(int)

    logger.info(f"开始训练模型: {config.model_type}")
    logger.info(f"特征数量: {X.shape[1]}, 样本数量: {X.shape[0]}")
    logger.info(f"正类样本: {y_bin.sum()}, 负类样本: {len(y_bin) - y_bin.sum()}")

    # 创建模型
    clf = create_model_pipeline(config.model_type)

    # 时间序列交叉验证
    tscv = TimeSeriesSplit(n_splits=config.n_splits)
    aucs = []

    for fold, (tr_idx, te_idx) in enumerate(tscv.split(X)):
        logger.info(f"训练折 {fold + 1}/{config.n_splits}")

        X_train, X_test = X.iloc[tr_idx], X.iloc[te_idx]
        y_train, y_test = y_bin.iloc[tr_idx], y_bin.iloc[te_idx]

        # 训练模型
        clf.fit(X_train, y_train)

        # 预测概率
        try:
            proba = clf.predict_proba(X_test)[:, 1]
            auc = roc_auc_score(y_test, proba)
            aucs.append(auc)
            logger.info(f"折 {fold + 1} AUC: {auc:.4f}")
        except Exception as e:
            logger.warning(f"折 {fold + 1} AUC计算失败: {e}")

    if len(aucs) > 0:
        mean_auc = np.mean(aucs)
        std_auc = np.std(aucs)
        logger.info(f"交叉验证 AUC: {mean_auc:.4f} ± {std_auc:.4f}")

    # 使用全部数据进行最终训练
    logger.info("使用全部数据进行最终训练")
    clf.fit(X, y_bin)

    return clf


def hyperparameter_tuning(X: pd.DataFrame, y: pd.Series,
                         model_type: str = "GradientBoostingClassifier",
                         param_grid: Optional[Dict] = None,
                         cv_splits: int = 3) -> Tuple[Pipeline, Dict]:
    """
    超参数调优

    Args:
        X: 特征数据
        y: 标签数据
        model_type: 模型类型
        param_grid: 参数网格
        cv_splits: 交叉验证折数

    Returns:
        (最佳模型, 最佳参数)
    """
    logger.info(f"开始超参数调优: {model_type}")

    # 转换为二分类
    y_bin = (y > 0).astype(int)

    # 默认参数网格
    if param_grid is None:
        if model_type == "GradientBoostingClassifier":
            param_grid = {
                'classifier__n_estimators': [50, 100, 200],
                'classifier__learning_rate': [0.05, 0.1, 0.2],
                'classifier__max_depth': [3, 5, 7]
            }
        elif model_type == "RandomForestClassifier":
            param_grid = {
                'classifier__n_estimators': [50, 100, 200],
                'classifier__max_depth': [None, 5, 10],
                'classifier__min_samples_split': [2, 5, 10]
            }
        elif model_type == "LogisticRegression":
            param_grid = {
                'classifier__C': [0.1, 1.0, 10.0],
                'classifier__max_iter': [1000, 2000]
            }
        else:
            param_grid = {}

    # 创建基础模型
    base_model = create_model_pipeline(model_type)

    # 网格搜索
    tscv = TimeSeriesSplit(n_splits=cv_splits)
    grid_search = GridSearchCV(
        base_model,
        param_grid,
        cv=tscv,
        scoring='roc_auc',
        n_jobs=-1,
        verbose=1
    )

    # 执行搜索
    grid_search.fit(X, y_bin)

    logger.info(f"最佳参数: {grid_search.best_params_}")
    logger.info(f"最佳分数: {grid_search.best_score_:.4f}")

    return grid_search.best_estimator_, grid_search.best_params_

