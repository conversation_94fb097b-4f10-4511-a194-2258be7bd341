"""
事件打标模块
实现三重障碍法和回踩标注
"""

from __future__ import annotations
import numpy as np
import pandas as pd
from typing import Optional
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config import SignalParams


def triple_barrier_labeling(df: pd.DataFrame, p: SignalParams,
                            col_trigger: str) -> pd.DataFrame:
    """
    对触发列（tl_long, tl_short, pl_trigger）产生的时点打标签: +1/-1/0

    Args:
        df: 包含OHLCV和信号的DataFrame
        p: 信号参数配置
        col_trigger: 触发列名

    Returns:
        包含标签的DataFrame
    """
    out = df.copy()
    labels = np.zeros(len(out), dtype=int)
    idxs = np.where(out[col_trigger].values == 1)[0]
    closes = out["close"].values
    atrs = out["atr"].values

    for i in idxs:
        entry = closes[i]
        d = int(np.clip(p.max_hold_bars, 1, 10000))

        if out["dir"].iloc[i] >= 0:  # 多向
            tp = entry + p.tp_atr * atrs[i]
            sl = entry - p.sl_atr * atrs[i]
        else:  # 空向
            tp = entry - p.tp_atr * atrs[i]
            sl = entry + p.sl_atr * atrs[i]

        j_end = min(i + d, len(out) - 1)
        label = 0

        for j in range(i + 1, j_end + 1):
            high = out["high"].iloc[j]
            low = out["low"].iloc[j]

            if out["dir"].iloc[i] >= 0:
                hit_tp = high >= tp
                hit_sl = low <= sl
            else:
                hit_tp = low <= tp
                hit_sl = high >= sl

            if hit_tp and not hit_sl:
                label = 1
                break
            if hit_sl and not hit_tp:
                label = -1
                break
            # 若同根bar均命中，按先到价难以判定；保守记 0

        labels[i] = label

    out[f"label_{col_trigger}"] = labels
    return out


def fixed_time_horizon_labeling(df: pd.DataFrame, horizon: int = 5,
                               threshold: float = 0.01) -> pd.DataFrame:
    """
    固定时间窗口标注法

    Args:
        df: OHLCV数据
        horizon: 前瞻时间窗口
        threshold: 价格变化阈值

    Returns:
        包含标签的DataFrame
    """
    result = df.copy()
    labels = np.zeros(len(result))

    for i in range(len(result) - horizon):
        current_price = result['close'].iloc[i]
        future_price = result['close'].iloc[i + horizon]

        price_change = (future_price - current_price) / current_price

        if price_change > threshold:
            labels[i] = 1  # 上涨
        elif price_change < -threshold:
            labels[i] = -1  # 下跌
        else:
            labels[i] = 0  # 横盘

    result['label_fixed_horizon'] = labels
    return result


def trend_scanning_labeling(df: pd.DataFrame, min_pct: float = 0.005,
                           max_pct: float = 0.05) -> pd.DataFrame:
    """
    趋势扫描标注法

    Args:
        df: OHLCV数据
        min_pct: 最小价格变化百分比
        max_pct: 最大价格变化百分比

    Returns:
        包含标签的DataFrame
    """
    result = df.copy()
    labels = np.zeros(len(result))

    for i in range(len(result) - 1):
        current_price = result['close'].iloc[i]

        # 向前扫描，寻找满足条件的价格变化
        for j in range(i + 1, len(result)):
            future_price = result['close'].iloc[j]
            price_change = (future_price - current_price) / current_price

            # 如果达到最大变化，停止扫描
            if abs(price_change) >= max_pct:
                if price_change > 0:
                    labels[i] = 1
                else:
                    labels[i] = -1
                break

            # 如果达到最小变化，记录标签并继续扫描
            elif abs(price_change) >= min_pct:
                if price_change > 0:
                    labels[i] = 1
                else:
                    labels[i] = -1

    result['label_trend_scan'] = labels
    return result


def volatility_adjusted_labeling(df: pd.DataFrame, lookback: int = 20,
                                multiplier: float = 1.0) -> pd.DataFrame:
    """
    波动率调整标注法

    Args:
        df: OHLCV数据
        lookback: 波动率计算回看期
        multiplier: 波动率倍数

    Returns:
        包含标签的DataFrame
    """
    result = df.copy()

    # 计算滚动波动率
    returns = result['close'].pct_change()
    volatility = returns.rolling(window=lookback).std()

    labels = np.zeros(len(result))

    for i in range(lookback, len(result) - 1):
        current_price = result['close'].iloc[i]
        vol = volatility.iloc[i]

        if pd.isna(vol) or vol == 0:
            continue

        # 动态阈值
        threshold = vol * multiplier

        # 向前看一期
        if i + 1 < len(result):
            future_price = result['close'].iloc[i + 1]
            price_change = (future_price - current_price) / current_price

            if price_change > threshold:
                labels[i] = 1
            elif price_change < -threshold:
                labels[i] = -1

    result['label_vol_adjusted'] = labels
    return result


def combine_labels(df: pd.DataFrame, label_columns: list,
                  method: str = 'majority') -> pd.DataFrame:
    """
    合并多个标签列

    Args:
        df: 包含多个标签列的DataFrame
        label_columns: 标签列名列表
        method: 合并方法 ('majority', 'unanimous', 'any_positive')

    Returns:
        包含合并标签的DataFrame
    """
    result = df.copy()

    # 提取标签数据
    label_data = result[label_columns].values
    combined_labels = np.zeros(len(result))

    for i in range(len(result)):
        row_labels = label_data[i]

        if method == 'majority':
            # 多数投票
            positive_count = np.sum(row_labels == 1)
            negative_count = np.sum(row_labels == -1)

            if positive_count > negative_count:
                combined_labels[i] = 1
            elif negative_count > positive_count:
                combined_labels[i] = -1
            else:
                combined_labels[i] = 0

        elif method == 'unanimous':
            # 一致同意
            if np.all(row_labels == 1):
                combined_labels[i] = 1
            elif np.all(row_labels == -1):
                combined_labels[i] = -1
            else:
                combined_labels[i] = 0

        elif method == 'any_positive':
            # 任何正信号
            if np.any(row_labels == 1):
                combined_labels[i] = 1
            elif np.any(row_labels == -1):
                combined_labels[i] = -1
            else:
                combined_labels[i] = 0

    result['label_combined'] = combined_labels
    return result

