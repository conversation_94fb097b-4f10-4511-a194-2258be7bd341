"""
通用工具函数模块
包含数据读取、技术指标计算等通用功能
"""

from __future__ import annotations
import numpy as np
import pandas as pd
from typing import Optional


def read_ohlcv_csv(path: str) -> pd.DataFrame:
    """
    读取OHLCV CSV文件，支持多种格式：
    1. 标准格式：timestamp, open, high, low, close, volume
    2. Binance格式：open_time, open, high, low, close, volume, close_time, ...
    """
    df = pd.read_csv(path)

    # 检查是否是Binance格式（包含open_time列）
    if 'open_time' in df.columns:
        # Binance格式处理
        df['datetime'] = pd.to_datetime(df['open_time'])
        df = df.set_index('datetime').sort_index()
        return df[["open", "high", "low", "close", "volume"]].astype(float)

    # 标准格式处理
    cols = {c.lower(): c for c in df.columns}
    for key in ["timestamp", "open", "high", "low", "close", "volume"]:
        assert key in {k.lower() for k in df.columns}, f"CSV 缺少列: {key}"

    # 统一列名
    df = df.rename(columns={cols.get("timestamp"): "timestamp",
                            cols.get("open"): "open",
                            cols.get("high"): "high",
                            cols.get("low"): "low",
                            cols.get("close"): "close",
                            cols.get("volume"): "volume"})

    # 时间戳转 datetime
    ts = df["timestamp"].values
    # 判断毫秒还是秒
    if np.nanmax(ts) > 1e12:
        df["datetime"] = pd.to_datetime(df["timestamp"], unit="ms", utc=True)
    else:
        df["datetime"] = pd.to_datetime(df["timestamp"], unit="s", utc=True)
    df = df.set_index("datetime").sort_index()
    return df[["open", "high", "low", "close", "volume"]].astype(float)


def ema(series: pd.Series, n: int) -> pd.Series:
    """计算指数移动平均线"""
    return series.ewm(span=n, adjust=False).mean()


def atr(df: pd.DataFrame, n: int = 14) -> pd.Series:
    """计算平均真实波幅 (ATR)"""
    high, low, close = df["high"], df["low"], df["close"].shift(1)
    tr = np.maximum(high - low, np.maximum((high - close).abs(), (low - close).abs()))
    return tr.ewm(span=n, adjust=False).mean()


def cross_over(a: pd.Series, b: pd.Series) -> pd.Series:
    """检测上穿信号"""
    return (a > b) & (a.shift(1) <= b.shift(1))


def cross_under(a: pd.Series, b: pd.Series) -> pd.Series:
    """检测下穿信号"""
    return (a < b) & (a.shift(1) >= b.shift(1))


def validate_dataframe(df: pd.DataFrame, required_columns: list) -> bool:
    """验证DataFrame是否包含必需的列"""
    missing_cols = [col for col in required_columns if col not in df.columns]
    if missing_cols:
        raise ValueError(f"DataFrame缺少必需的列: {missing_cols}")
    return True


def clean_data(df: pd.DataFrame) -> pd.DataFrame:
    """清理数据：处理无穷值和NaN"""
    df_clean = df.copy()
    # 替换无穷值为NaN
    df_clean = df_clean.replace([np.inf, -np.inf], np.nan)
    # 填充NaN值
    df_clean = df_clean.fillna(0)
    return df_clean


def calculate_returns(prices: pd.Series, periods: int = 1) -> pd.Series:
    """计算收益率"""
    return prices.pct_change(periods).fillna(0)


def normalize_features(df: pd.DataFrame, method: str = 'zscore') -> pd.DataFrame:
    """特征标准化"""
    if method == 'zscore':
        return (df - df.mean()) / df.std()
    elif method == 'minmax':
        return (df - df.min()) / (df.max() - df.min())
    else:
        raise ValueError(f"不支持的标准化方法: {method}")