"""
特征配置模块
允许用户配置要使用的特征集
"""

from __future__ import annotations
from typing import List, Dict, Optional, Callable
import pandas as pd
from dataclasses import dataclass, field

from features.custom_features import CUSTOM_FEATURES, get_custom_feature_function


@dataclass
class FeatureConfig:
    """
    特征配置类
    定义要使用的特征集合
    """
    
    # 基础特征开关
    use_basic_features: bool = True
    
    # 高级特征开关
    use_advanced_features: bool = True
    
    # 要使用的自定义特征列表
    custom_features: List[str] = field(default_factory=list)
    
    # 特征选择策略
    feature_selection_method: Optional[str] = None  # 'importance', 'correlation', None
    
    # 最大特征数量（用于特征选择）
    max_features: Optional[int] = None
    
    # 特征重要性阈值
    importance_threshold: float = 0.01
    
    # 相关性阈值（移除高相关性特征）
    correlation_threshold: float = 0.95
    
    def __post_init__(self):
        """初始化后验证配置"""
        self.validate_config()
    
    def validate_config(self):
        """验证配置的有效性"""
        # 验证自定义特征是否存在
        for feature_name in self.custom_features:
            if feature_name not in CUSTOM_FEATURES:
                raise ValueError(f"Custom feature '{feature_name}' not found. "
                               f"Available features: {list(CUSTOM_FEATURES.keys())}")
        
        # 验证特征选择方法
        valid_methods = ['importance', 'correlation', None]
        if self.feature_selection_method not in valid_methods:
            raise ValueError(f"Invalid feature selection method: {self.feature_selection_method}. "
                           f"Valid methods: {valid_methods}")
    
    def get_custom_feature_functions(self) -> Dict[str, Callable]:
        """
        获取自定义特征函数字典
        
        Returns:
            特征名称到函数的映射
        """
        return {name: get_custom_feature_function(name) for name in self.custom_features}
    
    def get_expected_feature_count(self) -> int:
        """
        获取期望的特征数量（不包括特征选择）
        
        Returns:
            特征数量
        """
        count = 0
        
        if self.use_basic_features:
            count += 26  # 基础特征数量
        
        if self.use_advanced_features:
            count += 7   # 高级特征数量
        
        count += len(self.custom_features)  # 自定义特征数量
        
        return count
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            'use_basic_features': self.use_basic_features,
            'use_advanced_features': self.use_advanced_features,
            'custom_features': self.custom_features,
            'feature_selection_method': self.feature_selection_method,
            'max_features': self.max_features,
            'importance_threshold': self.importance_threshold,
            'correlation_threshold': self.correlation_threshold,
        }
    
    @classmethod
    def from_dict(cls, config_dict: dict) -> 'FeatureConfig':
        """从字典创建配置对象"""
        return cls(**config_dict)


# 预定义的特征配置模板
class FeatureConfigTemplates:
    """预定义的特征配置模板"""
    
    @staticmethod
    def minimal() -> FeatureConfig:
        """最小特征集（仅基础特征）"""
        return FeatureConfig(
            use_basic_features=True,
            use_advanced_features=False,
            custom_features=[],
        )
    
    @staticmethod
    def standard() -> FeatureConfig:
        """标准特征集（基础+高级特征）"""
        return FeatureConfig(
            use_basic_features=True,
            use_advanced_features=True,
            custom_features=[],
        )
    
    @staticmethod
    def enhanced() -> FeatureConfig:
        """增强特征集（包含一些有用的自定义特征）"""
        return FeatureConfig(
            use_basic_features=True,
            use_advanced_features=True,
            custom_features=[
                "bollinger_squeeze",
                "volume_price_confirmation",
                "trend_consistency",
                "volatility_regime"
            ],
        )
    
    @staticmethod
    def comprehensive() -> FeatureConfig:
        """全面特征集（包含所有可用特征）"""
        return FeatureConfig(
            use_basic_features=True,
            use_advanced_features=True,
            custom_features=list(CUSTOM_FEATURES.keys()),
        )
    
    @staticmethod
    def optimized() -> FeatureConfig:
        """优化特征集（使用特征选择）"""
        return FeatureConfig(
            use_basic_features=True,
            use_advanced_features=True,
            custom_features=[
                "bollinger_squeeze",
                "volume_price_confirmation",
                "trend_consistency"
            ],
            feature_selection_method="importance",
            max_features=20,
            importance_threshold=0.02,
            correlation_threshold=0.9,
        )


def load_feature_config(config_path: Optional[str] = None) -> FeatureConfig:
    """
    加载特征配置
    
    Args:
        config_path: 配置文件路径，如果为None则使用默认配置
        
    Returns:
        特征配置对象
    """
    if config_path is None:
        # 使用默认标准配置
        return FeatureConfigTemplates.standard()
    
    try:
        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
        return FeatureConfig.from_dict(config_dict)
    except Exception as e:
        print(f"Warning: Failed to load feature config from {config_path}: {e}")
        print("Using default standard configuration.")
        return FeatureConfigTemplates.standard()


def save_feature_config(config: FeatureConfig, config_path: str):
    """
    保存特征配置到文件
    
    Args:
        config: 特征配置对象
        config_path: 保存路径
    """
    import json
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config.to_dict(), f, indent=2, ensure_ascii=False)


# 示例配置文件内容
EXAMPLE_CONFIG = {
    "use_basic_features": True,
    "use_advanced_features": True,
    "custom_features": [
        "bollinger_squeeze",
        "volume_price_confirmation",
        "trend_consistency"
    ],
    "feature_selection_method": "importance",
    "max_features": 25,
    "importance_threshold": 0.02,
    "correlation_threshold": 0.9
}


if __name__ == "__main__":
    # 示例用法
    print("=== 特征配置示例 ===")
    
    # 创建不同的配置
    configs = {
        "minimal": FeatureConfigTemplates.minimal(),
        "standard": FeatureConfigTemplates.standard(),
        "enhanced": FeatureConfigTemplates.enhanced(),
        "comprehensive": FeatureConfigTemplates.comprehensive(),
        "optimized": FeatureConfigTemplates.optimized(),
    }
    
    for name, config in configs.items():
        print(f"\n{name.upper()} 配置:")
        print(f"  期望特征数量: {config.get_expected_feature_count()}")
        print(f"  基础特征: {config.use_basic_features}")
        print(f"  高级特征: {config.use_advanced_features}")
        print(f"  自定义特征: {config.custom_features}")
        if config.feature_selection_method:
            print(f"  特征选择: {config.feature_selection_method}")
            print(f"  最大特征数: {config.max_features}")
    
    # 保存示例配置
    example_config = FeatureConfigTemplates.enhanced()
    print(f"\n示例配置字典:")
    print(example_config.to_dict())
