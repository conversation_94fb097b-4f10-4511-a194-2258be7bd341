"""
特征一致性使用示例
演示如何使用统一特征提取器确保训练和回测的特征一致性
"""

import sys
import os
import numpy as np
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from features.feature_extractor import FeatureExtractor
from features.indicators import add_all_indicators
from config.feature_config import FeatureConfig, FeatureConfigTemplates
from features.custom_features import add_custom_feature
from utils.config import SignalParams
from utils.logger import get_logger

logger = get_logger("feature_example")


def create_sample_data(n_samples: int = 1000) -> pd.DataFrame:
    """创建示例数据"""
    np.random.seed(42)
    
    dates = pd.date_range('2023-01-01', periods=n_samples, freq='1h')
    returns = np.random.normal(0, 0.01, n_samples)
    prices = 100 * np.exp(np.cumsum(returns))
    
    df = pd.DataFrame({
        'open': prices * (1 + np.random.normal(0, 0.001, n_samples)),
        'high': prices * (1 + np.abs(np.random.normal(0, 0.005, n_samples))),
        'low': prices * (1 - np.abs(np.random.normal(0, 0.005, n_samples))),
        'close': prices,
        'volume': np.random.lognormal(10, 1, n_samples),
    }, index=dates)
    
    # 确保OHLC关系正确
    df['high'] = np.maximum(df['high'], np.maximum(df['open'], df['close']))
    df['low'] = np.minimum(df['low'], np.minimum(df['open'], df['close']))
    
    return df


def example_1_basic_usage():
    """示例1：基本使用方法"""
    logger.info("=== 示例1：基本使用方法 ===")
    
    # 创建数据
    df = create_sample_data(500)
    
    # 添加技术指标
    signal_params = SignalParams()
    df_with_indicators = add_all_indicators(df, signal_params)
    
    # 使用标准配置
    config = FeatureConfigTemplates.standard()
    extractor = FeatureExtractor(signal_params, config)
    
    # 提取特征
    features = extractor.extract_features_from_dataframe(df_with_indicators)
    
    logger.info(f"特征数量: {len(features.columns)}")
    logger.info(f"特征名称: {list(features.columns)}")
    logger.info(f"数据形状: {features.shape}")
    
    return features


def example_2_custom_features():
    """示例2：自定义特征"""
    logger.info("=== 示例2：自定义特征 ===")
    
    # 定义自定义特征函数
    def price_volatility_ratio(df: pd.DataFrame) -> pd.Series:
        """价格波动率比率"""
        short_vol = df['close'].pct_change().rolling(5).std()
        long_vol = df['close'].pct_change().rolling(20).std()
        return short_vol / (long_vol + 1e-12)
    
    def volume_price_correlation(df: pd.DataFrame) -> pd.Series:
        """成交量价格相关性"""
        return df['volume'].rolling(20).corr(df['close'])
    
    # 注册自定义特征
    add_custom_feature("price_volatility_ratio", price_volatility_ratio)
    add_custom_feature("volume_price_correlation", volume_price_correlation)
    
    # 创建包含自定义特征的配置
    config = FeatureConfig(
        use_basic_features=True,
        use_advanced_features=True,
        custom_features=["price_volatility_ratio", "volume_price_correlation"]
    )
    
    # 创建数据和提取特征
    df = create_sample_data(500)
    signal_params = SignalParams()
    df_with_indicators = add_all_indicators(df, signal_params)
    
    extractor = FeatureExtractor(signal_params, config)
    features = extractor.extract_features_from_dataframe(df_with_indicators)
    
    logger.info(f"包含自定义特征的特征数量: {len(features.columns)}")
    logger.info(f"自定义特征: {config.custom_features}")
    
    return features


def example_3_feature_selection():
    """示例3：特征选择"""
    logger.info("=== 示例3：特征选择配置 ===")
    
    # 创建带特征选择的配置
    config = FeatureConfig(
        use_basic_features=True,
        use_advanced_features=True,
        custom_features=["bollinger_squeeze", "volume_price_confirmation"],
        feature_selection_method="importance",
        max_features=20,
        importance_threshold=0.02,
        correlation_threshold=0.9
    )
    
    logger.info(f"配置信息:")
    logger.info(f"  基础特征: {config.use_basic_features}")
    logger.info(f"  高级特征: {config.use_advanced_features}")
    logger.info(f"  自定义特征: {config.custom_features}")
    logger.info(f"  特征选择方法: {config.feature_selection_method}")
    logger.info(f"  最大特征数: {config.max_features}")
    logger.info(f"  期望特征数量: {config.get_expected_feature_count()}")
    
    return config


def example_4_configuration_templates():
    """示例4：配置模板对比"""
    logger.info("=== 示例4：配置模板对比 ===")
    
    templates = {
        "minimal": FeatureConfigTemplates.minimal(),
        "standard": FeatureConfigTemplates.standard(),
        "enhanced": FeatureConfigTemplates.enhanced(),
        "comprehensive": FeatureConfigTemplates.comprehensive(),
        "optimized": FeatureConfigTemplates.optimized(),
    }
    
    for name, config in templates.items():
        logger.info(f"{name.upper()} 配置:")
        logger.info(f"  期望特征数量: {config.get_expected_feature_count()}")
        logger.info(f"  基础特征: {config.use_basic_features}")
        logger.info(f"  高级特征: {config.use_advanced_features}")
        logger.info(f"  自定义特征数量: {len(config.custom_features)}")
        if config.feature_selection_method:
            logger.info(f"  特征选择: {config.feature_selection_method}")
        logger.info("")


def example_5_save_load_config():
    """示例5：保存和加载配置"""
    logger.info("=== 示例5：保存和加载配置 ===")
    
    # 创建配置
    config = FeatureConfig(
        use_basic_features=True,
        use_advanced_features=True,
        custom_features=["bollinger_squeeze", "trend_consistency"],
        feature_selection_method="importance",
        max_features=25
    )
    
    # 保存配置
    config_path = "temp_feature_config.json"
    from config.feature_config import save_feature_config, load_feature_config
    
    save_feature_config(config, config_path)
    logger.info(f"配置已保存到: {config_path}")
    
    # 加载配置
    loaded_config = load_feature_config(config_path)
    logger.info(f"配置已加载，特征数量: {loaded_config.get_expected_feature_count()}")
    
    # 清理临时文件
    if os.path.exists(config_path):
        os.remove(config_path)
        logger.info("临时配置文件已删除")


def example_6_feature_consistency_check():
    """示例6：特征一致性检查"""
    logger.info("=== 示例6：特征一致性检查 ===")
    
    # 创建数据
    df = create_sample_data(200)
    signal_params = SignalParams()
    df_with_indicators = add_all_indicators(df, signal_params)
    
    # 使用标准配置
    config = FeatureConfigTemplates.standard()
    extractor = FeatureExtractor(signal_params, config)
    
    # DataFrame方法提取特征
    features_df = extractor.extract_features_from_dataframe(df_with_indicators)
    
    logger.info(f"DataFrame方法特征数量: {len(features_df.columns)}")
    logger.info(f"特征名称: {extractor.get_feature_names()}")
    
    # 显示特征统计信息
    logger.info("\n特征统计信息:")
    for i, feature_name in enumerate(extractor.get_feature_names()[:10]):  # 只显示前10个
        if feature_name in features_df.columns:
            values = features_df[feature_name].dropna()
            if len(values) > 0:
                logger.info(f"  {feature_name}: 均值={values.mean():.4f}, 标准差={values.std():.4f}")


def main():
    """主函数"""
    logger.info("开始特征一致性使用示例")
    
    try:
        # 运行所有示例
        example_1_basic_usage()
        example_2_custom_features()
        example_3_feature_selection()
        example_4_configuration_templates()
        example_5_save_load_config()
        example_6_feature_consistency_check()
        
        logger.info("✅ 所有示例运行完成!")
        
    except Exception as e:
        logger.error(f"❌ 示例运行失败: {e}")
        raise


if __name__ == "__main__":
    main()
