"""
机器学习过滤策略模块
在基础策略基础上添加ML概率阈值过滤
"""

from __future__ import annotations
import os
import sys
import numpy as np
from typing import Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 可选依赖
try:
    import backtrader as bt
except ImportError:
    bt = None

import joblib
from strategy.base_strategy import BaseStrategy
from utils.logger import get_logger

logger = get_logger("backtest")


class MLFilterStrategy(BaseStrategy):
    """
    机器学习过滤策略
    在基础策略信号基础上使用ML模型进行过滤
    """

    params = dict(
        # 继承基础策略参数
        ma_s=24,
        ma_l=128,
        atr_n=14,
        tp_atr=3.0,
        sl_atr=1.5,
        max_hold_bars=24,
        delta=0.5,
        risk_perc=0.003,
        cash_start=10000,
        commission=0.0005,    # 手续费（由回测引擎使用）
        # ML过滤参数
        prob_threshold=0.65,  # 概率阈值
        model_path=None,      # 模型路径
    )

    def __init__(self):
        super().__init__()

        if not bt:
            return

        # 加载ML模型
        self.clf = None
        if self.p.model_path and os.path.exists(self.p.model_path):
            try:
                self.clf = joblib.load(self.p.model_path)
                logger.info(f"ML模型已加载: {self.p.model_path}")
            except Exception as e:
                logger.error(f"加载ML模型失败: {e}")
                self.clf = None
        else:
            logger.warning("未提供有效的模型路径，将使用基础策略")

    def _generate_signal(self, price: float, atr_val: float, direction: int) -> int:
        """
        生成带ML过滤的交易信号

        Args:
            price: 当前价格
            atr_val: ATR值
            direction: 趋势方向

        Returns:
            信号: 1(买入), -1(卖出), 0(无信号)
        """
        # 获取基础信号
        raw_signal = super()._generate_signal(price, atr_val, direction)

        if raw_signal == 0:
            return 0

        # 如果没有ML模型，直接返回基础信号
        if self.clf is None:
            return raw_signal

        # ML过滤
        try:
            features = self._extract_features()
            probability = float(self.clf.predict_proba(features)[:, 1])

            logger.debug(f"ML概率: {probability:.3f}, 阈值: {self.p.prob_threshold}")

            if probability >= self.p.prob_threshold:
                logger.info(f"ML过滤通过: 概率={probability:.3f}, 信号={raw_signal}")
                return raw_signal
            else:
                logger.info(f"ML过滤拒绝: 概率={probability:.3f} < {self.p.prob_threshold}")
                return 0

        except Exception as e:
            logger.error(f"ML过滤失败: {e}")
            return raw_signal  # 出错时使用原始信号

    def _extract_features(self) -> np.ndarray:
        """
        提取当前时刻的特征（与训练时的build_features保持一致）

        Returns:
            特征数组
        """
        # 获取当前指标值
        ma_s_val = float(self.ema_s[0])
        ma_l_val = float(self.ema_l[0])
        atr_val = float(self.atr[0]) + 1e-12
        close = float(self.data_close[0])

        # 构造特征（与训练时的build_features完全一致）
        features = {}

        # 均线特征
        features["ma_diff"] = ma_s_val - ma_l_val
        features["ma_ratio"] = ma_s_val / (ma_l_val + 1e-12) - 1
        features["ma_slope_s"] = self._get_slope(self.ema_s)
        features["ma_slope_l"] = self._get_slope(self.ema_l)

        # ATR特征
        features["atr"] = atr_val
        features["atr_ratio"] = atr_val / close

        # 布林带特征
        bb_width = self._get_bb_width()
        features["bb_width"] = bb_width
        features["bb_percent"] = self._get_bb_percent(close, bb_width)

        # 价格动量特征
        features["ret_1"] = self._get_return(1)
        features["ret_5"] = self._get_return(5)
        features["ret_10"] = self._get_return(10)

        # RSI特征（简化版本）
        features["rsi"] = self._get_rsi_approx()
        features["rsi_oversold"] = 1.0 if features["rsi"] < 30 else 0.0
        features["rsi_overbought"] = 1.0 if features["rsi"] > 70 else 0.0

        # MACD特征（简化版本）
        macd_val = self._get_macd_approx()
        features["macd"] = macd_val
        features["macd_signal"] = macd_val * 0.9  # 简化的信号线
        features["macd_histogram"] = macd_val * 0.1  # 简化的柱状图

        # 成交量特征（使用默认值）
        features["volume_ratio"] = 1.0

        # 方向特征
        features["dir"] = 1.0 if (ma_s_val - ma_l_val) > 0 else (-1.0 if (ma_s_val - ma_l_val) < 0 else 0.0)

        # 时间特征（使用默认值）
        features["hour"] = 12.0  # 默认中午
        features["dow"] = 2.0    # 默认周二
        features["month"] = 6.0  # 默认6月

        # 价格位置特征
        features["price_position_20"] = self._get_price_position(20)
        features["price_position_50"] = self._get_price_position(50)

        # 波动率特征
        features["volatility_5"] = self._get_volatility(5)
        features["volatility_20"] = self._get_volatility(20)

        # 特征顺序（与训练时保持一致）
        feature_order = [
            "ma_diff", "ma_ratio", "ma_slope_s", "ma_slope_l", "atr", "atr_ratio",
            "bb_width", "bb_percent", "ret_1", "ret_5", "ret_10",
            "rsi", "rsi_oversold", "rsi_overbought",
            "macd", "macd_signal", "macd_histogram",
            "volume_ratio", "dir", "hour", "dow", "month",
            "price_position_20", "price_position_50",
            "volatility_5", "volatility_20"
        ]

        # 转换为数组
        arr = np.array([features[k] for k in feature_order], dtype=float).reshape(1, -1)
        return arr

    def _get_slope(self, indicator) -> float:
        """获取指标斜率"""
        try:
            if len(indicator) > 1:
                return float(indicator[0] - indicator[-1])
            else:
                return 0.0
        except:
            return 0.0

    def _get_bb_width(self) -> float:
        """获取布林带宽度（近似）"""
        try:
            # 使用最近20根K线计算标准差
            closes = [float(self.data_close[-i]) for i in range(min(20, len(self.data_close)))]
            if len(closes) > 1:
                return 2.0 * float(np.std(closes))
            else:
                return 0.0
        except:
            return 0.0

    def _get_return(self, periods: int) -> float:
        """获取收益率"""
        try:
            if len(self.data_close) > periods:
                current = float(self.data_close[0])
                past = float(self.data_close[-periods])
                return current / past - 1.0
            else:
                return 0.0
        except:
            return 0.0

    def _get_bb_percent(self, close: float, bb_width: float) -> float:
        """获取布林带百分比位置"""
        try:
            # 简化计算：假设价格在布林带中间
            return 0.5
        except:
            return 0.5

    def _get_rsi_approx(self) -> float:
        """获取RSI近似值"""
        try:
            # 简化的RSI计算
            recent_returns = []
            for i in range(min(14, len(self.data_close))):
                if i + 1 < len(self.data_close):
                    ret = float(self.data_close[-i] / self.data_close[-i-1] - 1.0)
                    recent_returns.append(ret)

            if len(recent_returns) > 0:
                gains = [r for r in recent_returns if r > 0]
                losses = [-r for r in recent_returns if r < 0]

                avg_gain = np.mean(gains) if gains else 0.0
                avg_loss = np.mean(losses) if losses else 0.0

                if avg_loss == 0:
                    return 100.0

                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
                return rsi
            else:
                return 50.0
        except:
            return 50.0

    def _get_macd_approx(self) -> float:
        """获取MACD近似值"""
        try:
            # 简化的MACD：短期EMA - 长期EMA
            return float(self.ema_s[0] - self.ema_l[0])
        except:
            return 0.0

    def _get_price_position(self, periods: int) -> float:
        """获取价格在历史最高价中的位置"""
        try:
            current = float(self.data_close[0])
            max_prices = []
            for i in range(min(periods, len(self.data_close))):
                max_prices.append(float(self.data_close[-i]))

            if max_prices:
                max_price = max(max_prices)
                return current / max_price if max_price > 0 else 1.0
            else:
                return 1.0
        except:
            return 1.0

    def _get_volatility(self, periods: int) -> float:
        """获取波动率"""
        try:
            returns = []
            for i in range(min(periods, len(self.data_close) - 1)):
                if i + 1 < len(self.data_close):
                    ret = float(self.data_close[-i] / self.data_close[-i-1] - 1.0)
                    returns.append(ret)

            if len(returns) > 1:
                return float(np.std(returns))
            else:
                return 0.0
        except:
            return 0.0


class AdaptiveMLStrategy(MLFilterStrategy):
    """
    自适应ML策略
    根据市场条件动态调整概率阈值
    """

    params = dict(
        # 继承ML策略参数
        ma_s=24,
        ma_l=128,
        atr_n=14,
        tp_atr=3.0,
        sl_atr=1.5,
        max_hold_bars=24,
        delta=0.5,
        risk_perc=0.003,
        cash_start=10000,
        commission=0.0005,
        prob_threshold=0.65,
        model_path=None,
        # 自适应参数
        adaptive_window=50,      # 自适应窗口
        min_threshold=0.5,       # 最小阈值
        max_threshold=0.9,       # 最大阈值
        volatility_factor=0.1,   # 波动率调整因子
    )

    def __init__(self):
        super().__init__()

        if not bt:
            return

        # 自适应状态
        self.recent_returns = []
        self.recent_volatility = 0.0
        self.adaptive_threshold = self.p.prob_threshold

        logger.info("自适应ML策略初始化完成")

    def next(self):
        if not bt:
            return

        # 更新自适应阈值
        self._update_adaptive_threshold()

        # 调用父类的next方法
        super().next()

    def _update_adaptive_threshold(self):
        """更新自适应概率阈值"""
        # 计算最近收益率
        if len(self.data_close) > 1:
            current_return = float(self.data_close[0] / self.data_close[-1] - 1.0)
            self.recent_returns.append(current_return)

            # 保持窗口大小
            if len(self.recent_returns) > self.p.adaptive_window:
                self.recent_returns.pop(0)

            # 计算波动率
            if len(self.recent_returns) > 5:
                self.recent_volatility = float(np.std(self.recent_returns))

                # 根据波动率调整阈值
                # 高波动率时提高阈值（更保守）
                volatility_adjustment = self.recent_volatility * self.p.volatility_factor
                self.adaptive_threshold = np.clip(
                    self.p.prob_threshold + volatility_adjustment,
                    self.p.min_threshold,
                    self.p.max_threshold
                )

                logger.debug(f"自适应阈值: {self.adaptive_threshold:.3f}, 波动率: {self.recent_volatility:.4f}")

    def _generate_signal(self, price: float, atr_val: float, direction: int) -> int:
        """
        使用自适应阈值生成信号
        """
        # 获取基础信号
        raw_signal = BaseStrategy._generate_signal(self, price, atr_val, direction)

        if raw_signal == 0:
            return 0

        # 如果没有ML模型，直接返回基础信号
        if self.clf is None:
            return raw_signal

        # ML过滤（使用自适应阈值）
        try:
            features = self._extract_features()
            probability = float(self.clf.predict_proba(features)[:, 1])

            if probability >= self.adaptive_threshold:
                logger.info(f"自适应ML过滤通过: 概率={probability:.3f}, 阈值={self.adaptive_threshold:.3f}")
                return raw_signal
            else:
                logger.info(f"自适应ML过滤拒绝: 概率={probability:.3f} < {self.adaptive_threshold:.3f}")
                return 0

        except Exception as e:
            logger.error(f"自适应ML过滤失败: {e}")
            return raw_signal

