"""
机器学习过滤策略模块
在基础策略基础上添加ML概率阈值过滤
"""

from __future__ import annotations
import os
import sys
import numpy as np
from typing import Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 可选依赖
try:
    import backtrader as bt
except ImportError:
    bt = None

import joblib
from strategy.base_strategy import BaseStrategy
from features.feature_extractor import FeatureExtractor
from utils.logger import get_logger

logger = get_logger("backtest")


class MLFilterStrategy(BaseStrategy):
    """
    机器学习过滤策略
    在基础策略信号基础上使用ML模型进行过滤
    """

    params = dict(
        # 继承基础策略参数
        ma_s=24,
        ma_l=128,
        atr_n=14,
        tp_atr=3.0,
        sl_atr=1.5,
        max_hold_bars=24,
        delta=0.5,
        risk_perc=0.003,
        cash_start=10000,
        commission=0.0005,    # 手续费（由回测引擎使用）
        # ML过滤参数
        prob_threshold=0.65,  # 概率阈值
        model_path=None,      # 模型路径
    )

    def __init__(self):
        super().__init__()

        if not bt:
            return

        # 初始化特征提取器
        self.feature_extractor = FeatureExtractor()

        # 加载ML模型
        self.clf = None
        if self.p.model_path and os.path.exists(self.p.model_path):
            try:
                self.clf = joblib.load(self.p.model_path)
                logger.info(f"ML模型已加载: {self.p.model_path}")
                logger.info(f"期望特征数量: {self.feature_extractor.get_feature_count()}")
            except Exception as e:
                logger.error(f"加载ML模型失败: {e}")
                self.clf = None
        else:
            logger.warning("未提供有效的模型路径，将使用基础策略")

    def _generate_signal(self, price: float, atr_val: float, direction: int) -> int:
        """
        生成带ML过滤的交易信号

        Args:
            price: 当前价格
            atr_val: ATR值
            direction: 趋势方向

        Returns:
            信号: 1(买入), -1(卖出), 0(无信号)
        """
        # 获取基础信号
        raw_signal = super()._generate_signal(price, atr_val, direction)

        if raw_signal == 0:
            return 0

        # 如果没有ML模型，直接返回基础信号
        if self.clf is None:
            return raw_signal

        # ML过滤
        try:
            features = self.feature_extractor.extract_features_from_backtrader(self)
            probability = float(self.clf.predict_proba(features)[:, 1])

            logger.debug(f"ML概率: {probability:.3f}, 阈值: {self.p.prob_threshold}")
            logger.debug(f"特征数量: {features.shape[1]}")

            if probability >= self.p.prob_threshold:
                logger.info(f"ML过滤通过: 概率={probability:.3f}, 信号={raw_signal}")
                return raw_signal
            else:
                logger.info(f"ML过滤拒绝: 概率={probability:.3f} < {self.p.prob_threshold}")
                return 0

        except Exception as e:
            logger.error(f"ML过滤失败: {e}")
            return raw_signal  # 出错时使用原始信号




class AdaptiveMLStrategy(MLFilterStrategy):
    """
    自适应ML策略
    根据市场条件动态调整概率阈值
    """

    params = dict(
        # 继承ML策略参数
        ma_s=24,
        ma_l=128,
        atr_n=14,
        tp_atr=3.0,
        sl_atr=1.5,
        max_hold_bars=24,
        delta=0.5,
        risk_perc=0.003,
        cash_start=10000,
        commission=0.0005,
        prob_threshold=0.65,
        model_path=None,
        # 自适应参数
        adaptive_window=50,      # 自适应窗口
        min_threshold=0.5,       # 最小阈值
        max_threshold=0.9,       # 最大阈值
        volatility_factor=0.1,   # 波动率调整因子
    )

    def __init__(self):
        super().__init__()

        if not bt:
            return

        # 自适应状态
        self.recent_returns = []
        self.recent_volatility = 0.0
        self.adaptive_threshold = self.p.prob_threshold

        logger.info("自适应ML策略初始化完成")

    def next(self):
        if not bt:
            return

        # 更新自适应阈值
        self._update_adaptive_threshold()

        # 调用父类的next方法
        super().next()

    def _update_adaptive_threshold(self):
        """更新自适应概率阈值"""
        # 计算最近收益率
        if len(self.data_close) > 1:
            current_return = float(self.data_close[0] / self.data_close[-1] - 1.0)
            self.recent_returns.append(current_return)

            # 保持窗口大小
            if len(self.recent_returns) > self.p.adaptive_window:
                self.recent_returns.pop(0)

            # 计算波动率
            if len(self.recent_returns) > 5:
                self.recent_volatility = float(np.std(self.recent_returns))

                # 根据波动率调整阈值
                # 高波动率时提高阈值（更保守）
                volatility_adjustment = self.recent_volatility * self.p.volatility_factor
                self.adaptive_threshold = np.clip(
                    self.p.prob_threshold + volatility_adjustment,
                    self.p.min_threshold,
                    self.p.max_threshold
                )

                logger.debug(f"自适应阈值: {self.adaptive_threshold:.3f}, 波动率: {self.recent_volatility:.4f}")

    def _generate_signal(self, price: float, atr_val: float, direction: int) -> int:
        """
        使用自适应阈值生成信号
        """
        # 获取基础信号
        raw_signal = BaseStrategy._generate_signal(self, price, atr_val, direction)

        if raw_signal == 0:
            return 0

        # 如果没有ML模型，直接返回基础信号
        if self.clf is None:
            return raw_signal

        # ML过滤（使用自适应阈值）
        try:
            features = self.feature_extractor.extract_features_from_backtrader(self)
            probability = float(self.clf.predict_proba(features)[:, 1])

            if probability >= self.adaptive_threshold:
                logger.info(f"自适应ML过滤通过: 概率={probability:.3f}, 阈值={self.adaptive_threshold:.3f}")
                return raw_signal
            else:
                logger.info(f"自适应ML过滤拒绝: 概率={probability:.3f} < {self.adaptive_threshold:.3f}")
                return 0

        except Exception as e:
            logger.error(f"自适应ML过滤失败: {e}")
            return raw_signal

