"""
回测运行模块
提供Backtrader回测的主入口和批量回测功能
"""

from __future__ import annotations
import os
import sys
from typing import Dict, Any, Optional, List
import pandas as pd
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 可选依赖
try:
    import backtrader as bt
except ImportError:
    bt = None

from strategy.base_strategy import BaseStrategy
from strategy.ml_filter import MLFilterStrategy, AdaptiveMLStrategy
from utils.config import BacktestConfig
from utils.logger import get_logger

logger = get_logger("backtest")


def run_backtest(df: pd.DataFrame, strategy_class=None, params: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
    """
    运行单次回测

    Args:
        df: OHLCV数据
        strategy_class: 策略类
        params: 策略参数

    Returns:
        回测结果指标
    """
    if not bt:
        raise RuntimeError("需要安装 backtrader 才能回测")

    if strategy_class is None:
        strategy_class = BaseStrategy

    if params is None:
        params = {}

    logger.info(f"开始回测: {strategy_class.__name__}")
    logger.info(f"数据范围: {df.index[0]} 到 {df.index[-1]}")
    logger.info(f"数据点数: {len(df)}")

    # 创建Cerebro引擎
    cerebro = bt.Cerebro(stdstats=False)

    # 添加数据
    data = bt.feeds.PandasData(dataname=df)
    cerebro.adddata(data)

    # 设置初始资金和手续费
    initial_cash = params.get("cash_start", 10000.0)
    commission = params.get("commission", 0.0005)

    cerebro.broker.setcash(initial_cash)
    cerebro.broker.setcommission(commission=commission)

    # 添加策略（现在策略类已经定义了所有必要的参数）
    cerebro.addstrategy(strategy_class, **params)

    # 添加分析器
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
    cerebro.addanalyzer(bt.analyzers.SharpeRatio_A, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
    cerebro.addanalyzer(bt.analyzers.SQN, _name='sqn')

    # 运行回测
    logger.info("执行回测...")
    results = cerebro.run(maxcpus=1)
    strategy_result = results[0]

    # 获取最终资产价值
    final_value = cerebro.broker.getvalue()

    # 提取分析结果
    metrics = extract_backtest_metrics(strategy_result, initial_cash, final_value)

    logger.info("回测完成")
    logger.info(f"最终资产: {final_value:.2f}")
    logger.info(f"总收益率: {metrics['total_return']:.2%}")
    logger.info(f"夏普比率: {metrics['sharpe_ratio']:.4f}")
    logger.info(f"最大回撤: {metrics['max_drawdown']:.2%}")

    return metrics


def extract_backtest_metrics(strategy_result, initial_cash: float, final_value: float) -> Dict[str, float]:
    """
    提取回测指标

    Args:
        strategy_result: 策略运行结果
        initial_cash: 初始资金
        final_value: 最终资产价值

    Returns:
        指标字典
    """
    # 基础指标
    total_return = (final_value - initial_cash) / initial_cash

    # 交易分析
    trade_analyzer = strategy_result.analyzers.trades.get_analysis()
    total_trades = trade_analyzer.get('total', {}).get('total', 0)
    won_trades = trade_analyzer.get('won', {}).get('total', 0)
    lost_trades = trade_analyzer.get('lost', {}).get('total', 0)

    win_rate = won_trades / total_trades if total_trades > 0 else 0

    # 夏普比率
    sharpe_analysis = strategy_result.analyzers.sharpe.get_analysis()
    sharpe_ratio = sharpe_analysis.get('sharperatio', np.nan)
    if sharpe_ratio is None:
        sharpe_ratio = np.nan

    # 回撤分析
    drawdown_analysis = strategy_result.analyzers.drawdown.get_analysis()
    max_drawdown = drawdown_analysis.get('max', {}).get('drawdown', 0) / 100

    # 收益分析
    returns_analysis = strategy_result.analyzers.returns.get_analysis()

    # SQN (System Quality Number)
    sqn_analysis = strategy_result.analyzers.sqn.get_analysis()
    sqn = sqn_analysis.get('sqn', np.nan)

    metrics = {
        'total_return': float(total_return),
        'final_value': float(final_value),
        'sharpe_ratio': float(sharpe_ratio) if not np.isnan(sharpe_ratio) else 0.0,
        'max_drawdown': float(max_drawdown),
        'total_trades': int(total_trades),
        'win_rate': float(win_rate),
        'won_trades': int(won_trades),
        'lost_trades': int(lost_trades),
        'sqn': float(sqn) if not np.isnan(sqn) else 0.0,
    }

    return metrics


def batch_backtest(df: pd.DataFrame, strategy_configs: List[Dict[str, Any]]) -> pd.DataFrame:
    """
    批量回测

    Args:
        df: OHLCV数据
        strategy_configs: 策略配置列表

    Returns:
        回测结果DataFrame
    """
    logger.info(f"开始批量回测，共{len(strategy_configs)}个配置")

    results = []

    for i, config in enumerate(strategy_configs):
        logger.info(f"回测配置 {i+1}/{len(strategy_configs)}")

        strategy_class = config.get('strategy_class', BaseStrategy)
        params = config.get('params', {})
        config_name = config.get('name', f'Config_{i+1}')

        try:
            metrics = run_backtest(df, strategy_class, params)
            metrics['config_name'] = config_name
            metrics['strategy_class'] = strategy_class.__name__
            results.append(metrics)

        except Exception as e:
            logger.error(f"配置 {config_name} 回测失败: {e}")
            # 添加失败记录
            failed_metrics = {
                'config_name': config_name,
                'strategy_class': strategy_class.__name__,
                'total_return': np.nan,
                'final_value': np.nan,
                'sharpe_ratio': np.nan,
                'max_drawdown': np.nan,
                'total_trades': 0,
                'win_rate': np.nan,
                'won_trades': 0,
                'lost_trades': 0,
                'sqn': np.nan,
                'error': str(e)
            }
            results.append(failed_metrics)

    results_df = pd.DataFrame(results)

    # 按夏普比率排序
    results_df = results_df.sort_values('sharpe_ratio', ascending=False, na_last=True)

    logger.info("批量回测完成")
    logger.info(f"最佳配置: {results_df.iloc[0]['config_name']}")
    logger.info(f"最佳夏普比率: {results_df.iloc[0]['sharpe_ratio']:.4f}")

    return results_df


def parameter_sweep(df: pd.DataFrame, strategy_class=None, param_ranges: Optional[Dict] = None) -> pd.DataFrame:
    """
    参数扫描回测

    Args:
        df: OHLCV数据
        strategy_class: 策略类
        param_ranges: 参数范围字典

    Returns:
        参数扫描结果DataFrame
    """
    if strategy_class is None:
        strategy_class = BaseStrategy

    if param_ranges is None:
        param_ranges = {
            'ma_s': [12, 24, 36],
            'ma_l': [64, 128, 256],
            'tp_atr': [2.0, 3.0, 4.0],
            'sl_atr': [1.0, 1.5, 2.0]
        }

    logger.info(f"开始参数扫描: {strategy_class.__name__}")

    # 生成参数组合
    import itertools

    param_names = list(param_ranges.keys())
    param_values = list(param_ranges.values())
    param_combinations = list(itertools.product(*param_values))

    logger.info(f"参数组合数量: {len(param_combinations)}")

    # 构建策略配置
    strategy_configs = []
    for i, combination in enumerate(param_combinations):
        params = dict(zip(param_names, combination))
        config = {
            'name': f'Sweep_{i+1}',
            'strategy_class': strategy_class,
            'params': params
        }
        strategy_configs.append(config)

    # 执行批量回测
    results_df = batch_backtest(df, strategy_configs)

    # 添加参数列
    for i, param_name in enumerate(param_names):
        results_df[param_name] = [combo[i] for combo in param_combinations]

    return results_df


def walk_forward_analysis(df: pd.DataFrame, strategy_class=None, params: Optional[Dict] = None,
                         train_periods: int = 252, test_periods: int = 63) -> pd.DataFrame:
    """
    走势前进分析

    Args:
        df: OHLCV数据
        strategy_class: 策略类
        params: 策略参数
        train_periods: 训练期长度
        test_periods: 测试期长度

    Returns:
        走势前进分析结果
    """
    if strategy_class is None:
        strategy_class = BaseStrategy

    if params is None:
        params = {}

    logger.info("开始走势前进分析")
    logger.info(f"训练期: {train_periods}天, 测试期: {test_periods}天")

    results = []

    # 计算分析窗口
    total_periods = train_periods + test_periods
    start_idx = 0

    while start_idx + total_periods <= len(df):
        # 分割数据
        train_end = start_idx + train_periods
        test_end = start_idx + total_periods

        train_data = df.iloc[start_idx:train_end]
        test_data = df.iloc[train_end:test_end]

        logger.info(f"分析窗口: {train_data.index[0]} - {test_data.index[-1]}")

        try:
            # 在测试期运行回测
            metrics = run_backtest(test_data, strategy_class, params)
            metrics['train_start'] = train_data.index[0]
            metrics['train_end'] = train_data.index[-1]
            metrics['test_start'] = test_data.index[0]
            metrics['test_end'] = test_data.index[-1]

            results.append(metrics)

        except Exception as e:
            logger.error(f"走势前进分析窗口失败: {e}")

        # 移动窗口
        start_idx += test_periods

    results_df = pd.DataFrame(results)

    if len(results_df) > 0:
        logger.info("走势前进分析完成")
        logger.info(f"平均收益率: {results_df['total_return'].mean():.2%}")
        logger.info(f"平均夏普比率: {results_df['sharpe_ratio'].mean():.4f}")
        logger.info(f"胜率: {(results_df['total_return'] > 0).mean():.2%}")

    return results_df

