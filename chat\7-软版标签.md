帮你再加一个 **“软版标签”**（即返回未来收益率，而不是三分类），这样你既能训练分类器，也能训练回归模型。


##  `soft_labeling()` 的位置

建议就放在 **`utils/labeling.py`** 里：

* 它本质上是“三重边界打标”的一个**改良版本**
* 允许同时生成 **分类标签 (上涨/下跌/中性)** 和 **回归目标 (未来收益率)**
* 属于**通用打标方法**，所以放在 utils 更合理
* 在 `features/labeling.py` 里，只是 import 它，然后组合成特征表

✅ 我可以帮你在 `utils/labeling.py` 里直接加一个 `soft_labeling()`，和 `triple_barrier_labeling()` 并列。

好的 👍 那我就直接帮你在 `utils/labeling.py` 里加上 `soft_labeling()`，并且在注释里解释它和 `features/labeling.py` 的区别，避免未来混淆。

下面是完整代码示例：

```python
import numpy as np
import pandas as pd


# =============================
# 三重边界打标 (分类标签)
# =============================
def triple_barrier_labeling(prices: pd.Series,
                            up_threshold: float = 0.01,
                            down_threshold: float = -0.01,
                            max_holding: int = 30) -> pd.Series:
    """
    三重边界打标方法 (分类标签)
    标签取值: {1: 上涨, -1: 下跌, 0: 中性}

    Parameters
    ----------
    prices : pd.Series
        K线收盘价序列 (按时间升序)
    up_threshold : float
        上涨触发阈值 (默认 +1%)
    down_threshold : float
        下跌触发阈值 (默认 -1%)
    max_holding : int
        向前看的最大持仓周期数 (如 30 根 K 线)

    Returns
    -------
    labels : pd.Series
        每根 K 线对应的标签
    """
    labels = pd.Series(index=prices.index, dtype=int)
    
    for i in range(len(prices)):
        start_price = prices.iloc[i]
        future_window = prices.iloc[i+1 : i+1+max_holding]
        if future_window.empty:
            labels.iloc[i] = 0
            continue

        rel_change = (future_window / start_price - 1).values

        if (rel_change >= up_threshold).any():
            labels.iloc[i] = 1
        elif (rel_change <= down_threshold).any():
            labels.iloc[i] = -1
        else:
            labels.iloc[i] = 0

    return labels


# =============================
# 软标签打标 (分类 + 回归)
# =============================
def soft_labeling(prices: pd.Series,
                  max_holding: int = 30,
                  upper: float = 0.01,
                  lower: float = -0.01) -> pd.DataFrame:
    """
    软版打标方法 (同时提供分类 + 回归标签)

    分类标签: {1: 上涨, -1: 下跌, 0: 中性}
    回归标签: future_return (未来 max_holding 内的最大收益率)

    Parameters
    ----------
    prices : pd.Series
        K线收盘价序列
    max_holding : int
        向前看的最大持仓周期数
    upper : float
        上涨阈值 (用于分类)
    lower : float
        下跌阈值 (用于分类)

    Returns
    -------
    labels : pd.DataFrame
        包含两列:
            - label_cls: 分类标签 {+1, 0, -1}
            - label_reg: 回归目标 (未来收益率)
    """
    label_cls = pd.Series(index=prices.index, dtype=int)
    label_reg = pd.Series(index=prices.index, dtype=float)

    for i in range(len(prices)):
        start_price = prices.iloc[i]
        future_window = prices.iloc[i+1 : i+1+max_holding]
        if future_window.empty:
            label_cls.iloc[i] = 0
            label_reg.iloc[i] = 0.0
            continue

        rel_change = (future_window / start_price - 1).values

        # 分类：先涨到上界 or 先跌到下界
        if (rel_change >= upper).any():
            label_cls.iloc[i] = 1
        elif (rel_change <= lower).any():
            label_cls.iloc[i] = -1
        else:
            label_cls.iloc[i] = 0

        # 回归：未来窗口的最大收益率
        label_reg.iloc[i] = rel_change.max()

    return pd.DataFrame({
        "label_cls": label_cls,
        "label_reg": label_reg
    })


# ============================================
# 说明
# - 本文件 (utils/labeling.py): 只放通用的打标函数
# - features/labeling.py: 会调用这些函数，把标签拼接到特征表里
# ============================================
```

我在 `utils/labeling.py` 里给你加了 **三重边界打标** 和 **软标签打标** 两个函数：

* `triple_barrier_labeling()` → 分类任务用，传统三重边界逻辑。
* `soft_labeling()` → 同时返回分类标签（+1/-1/0）和回归目标（未来收益率），方便你在模型里做多任务学习。


