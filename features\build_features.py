"""
特征构建模块
合并指标和标签，生成训练用特征表
"""

from __future__ import annotations
import numpy as np
import pandas as pd
from typing import Optional, List
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.helpers import clean_data
from features.indicators import add_all_indicators
from features.labeling import triple_barrier_labeling, combine_labels
from features.feature_extractor import FeatureExtractor
from utils.config import SignalParams


def build_features(df: pd.DataFrame, signal_params: Optional[SignalParams] = None) -> pd.DataFrame:
    """
    构建机器学习特征（使用统一特征提取器）

    Args:
        df: 包含指标的DataFrame
        signal_params: 信号参数配置

    Returns:
        特征DataFrame
    """
    extractor = FeatureExtractor(signal_params)
    return extractor.extract_features_from_dataframe(df)


def build_comprehensive_features(df: pd.DataFrame, signal_params: Optional[SignalParams] = None) -> pd.DataFrame:
    """
    构建全面的特征集（使用统一特征提取器）

    Args:
        df: OHLCV数据
        signal_params: 信号参数配置

    Returns:
        包含所有特征的DataFrame
    """
    if signal_params is None:
        signal_params = SignalParams()

    # 添加所有技术指标
    df_with_indicators = add_all_indicators(df, signal_params)

    # 使用统一特征提取器
    extractor = FeatureExtractor(signal_params)
    features = extractor.extract_features_from_dataframe(df_with_indicators)

    return features


def add_advanced_features(features: pd.DataFrame, df: pd.DataFrame) -> pd.DataFrame:
    """
    添加高级特征

    Args:
        features: 基础特征DataFrame
        df: 原始数据DataFrame

    Returns:
        包含高级特征的DataFrame
    """
    result = features.copy()

    # 价格通道特征
    result["price_channel_20"] = (df["close"] - df["close"].rolling(20).min()) / \
                                (df["close"].rolling(20).max() - df["close"].rolling(20).min() + 1e-12)

    # 趋势强度特征
    result["trend_strength"] = abs(result["ma_diff"]) / (result["atr"] + 1e-12)

    # 动量发散特征
    if "macd_histogram" in result.columns:
        result["momentum_divergence"] = result["macd_histogram"].diff()

    # 成交量价格趋势
    if "volume_ratio" in result.columns:
        result["volume_price_trend"] = result["volume_ratio"] * result["ret_1"]

    # 波动率突破特征
    if "volatility_20" in result.columns:
        vol_ma = result["volatility_20"].rolling(20).mean()
        result["volatility_breakout"] = result["volatility_20"] / (vol_ma + 1e-12)

    # 价格加速度
    result["price_acceleration"] = result["ret_1"].diff()

    # 均线距离标准化
    if "atr" in result.columns:
        result["ma_distance_normalized"] = result["ma_diff"] / (result["atr"] + 1e-12)

    return result


def create_training_dataset(df: pd.DataFrame, signal_params: SignalParams,
                           label_methods: Optional[List[str]] = None) -> tuple:
    """
    创建完整的训练数据集

    Args:
        df: OHLCV数据
        signal_params: 信号参数配置
        label_methods: 标注方法列表

    Returns:
        (features, labels) 元组
    """
    if label_methods is None:
        label_methods = ['tl_long', 'tl_short', 'pl_trigger']

    # 构建特征
    features = build_comprehensive_features(df, signal_params)

    # 添加指标用于标注
    df_with_signals = add_all_indicators(df, signal_params)

    # 生成标签
    label_columns = []
    for method in label_methods:
        if method in df_with_signals.columns:
            labeled_df = triple_barrier_labeling(df_with_signals, signal_params, method)
            label_col = f"label_{method}"
            if label_col in labeled_df.columns:
                features[label_col] = labeled_df[label_col]
                label_columns.append(label_col)

    # 合并标签
    if len(label_columns) > 1:
        features_with_labels = combine_labels(features, label_columns, method='majority')
        final_labels = features_with_labels['label_combined']
    elif len(label_columns) == 1:
        final_labels = features[label_columns[0]]
    else:
        # 如果没有标签，创建一个全零的标签
        final_labels = pd.Series(0, index=features.index)

    # 移除标签列，只保留特征
    feature_columns = [col for col in features.columns if not col.startswith('label_')]
    final_features = features[feature_columns]

    # 过滤早期缺失数据
    mask = final_features.index >= final_features.index[100] if len(final_features) > 100 else \
           pd.Series(True, index=final_features.index)

    return final_features[mask], final_labels[mask]


def feature_importance_analysis(features: pd.DataFrame, labels: pd.Series) -> pd.DataFrame:
    """
    特征重要性分析

    Args:
        features: 特征DataFrame
        labels: 标签Series

    Returns:
        特征重要性DataFrame
    """
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.feature_selection import mutual_info_classif

    # 准备数据
    X = features.fillna(0)
    y = (labels > 0).astype(int)  # 转换为二分类

    # 随机森林特征重要性
    rf = RandomForestClassifier(n_estimators=100, random_state=42)
    rf.fit(X, y)
    rf_importance = rf.feature_importances_

    # 互信息特征重要性
    mi_importance = mutual_info_classif(X, y, random_state=42)

    # 创建重要性DataFrame
    importance_df = pd.DataFrame({
        'feature': features.columns,
        'rf_importance': rf_importance,
        'mi_importance': mi_importance
    })

    # 计算综合重要性分数
    importance_df['combined_score'] = (importance_df['rf_importance'] + importance_df['mi_importance']) / 2

    # 按重要性排序
    importance_df = importance_df.sort_values('combined_score', ascending=False)

    return importance_df

