"""
实盘交易经纪商模块
封装ccxt的下单、撤单、查询功能
"""

from __future__ import annotations
import sys
import os
from typing import Optional, Dict, Any, List
import time
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 可选依赖
try:
    import ccxt
except ImportError:
    ccxt = None

from utils.logger import get_logger

logger = get_logger("live")


class Broker:
    """交易经纪商封装类"""

    def __init__(self, exchange_name: str, api_key: str, api_secret: str,
                 sandbox: bool = True, rate_limit: bool = True):
        """
        初始化经纪商

        Args:
            exchange_name: 交易所名称
            api_key: API密钥
            api_secret: API密钥
            sandbox: 是否使用沙盒环境
            rate_limit: 是否启用限速
        """
        if not ccxt:
            raise RuntimeError("需要安装 ccxt 才能进行实盘交易")

        self.exchange_name = exchange_name

        # 创建交易所实例
        exchange_class = getattr(ccxt, exchange_name)
        self.exchange = exchange_class({
            'apiKey': api_key,
            'secret': api_secret,
            'enableRateLimit': rate_limit,
            'sandbox': sandbox,
        })

        logger.info(f"初始化交易所: {exchange_name}, 沙盒模式: {sandbox}")

        # 验证连接
        try:
            self.exchange.load_markets()
            logger.info("交易所连接成功")
        except Exception as e:
            logger.error(f"交易所连接失败: {e}")
            raise

    def get_balance(self) -> Dict[str, float]:
        """获取账户余额"""
        try:
            balance = self.exchange.fetch_balance()
            return balance['total']
        except Exception as e:
            logger.error(f"获取余额失败: {e}")
            return {}

    def get_positions(self) -> List[Dict]:
        """获取持仓信息"""
        try:
            positions = self.exchange.fetch_positions()
            # 过滤掉零持仓
            active_positions = [pos for pos in positions if float(pos['size']) != 0]
            return active_positions
        except Exception as e:
            logger.error(f"获取持仓失败: {e}")
            return []

    def fetch_ohlcv(self, symbol: str, timeframe: str = '5m', limit: int = 100) -> pd.DataFrame:
        """获取OHLCV数据"""
        try:
            ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe=timeframe, limit=limit)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True)
            df = df.set_index('datetime')[['open', 'high', 'low', 'close', 'volume']]
            return df.astype(float)
        except Exception as e:
            logger.error(f"获取OHLCV数据失败: {e}")
            return pd.DataFrame()

    def create_market_order(self, symbol: str, side: str, amount: float) -> Optional[Dict]:
        """创建市价单"""
        try:
            order = self.exchange.create_market_order(symbol, side, amount)
            logger.info(f"市价单已创建: {symbol} {side} {amount}")
            return order
        except Exception as e:
            logger.error(f"创建市价单失败: {e}")
            return None

    def create_limit_order(self, symbol: str, side: str, amount: float, price: float) -> Optional[Dict]:
        """创建限价单"""
        try:
            order = self.exchange.create_limit_order(symbol, side, amount, price)
            logger.info(f"限价单已创建: {symbol} {side} {amount} @ {price}")
            return order
        except Exception as e:
            logger.error(f"创建限价单失败: {e}")
            return None

    def cancel_order(self, order_id: str, symbol: str) -> bool:
        """撤销订单"""
        try:
            self.exchange.cancel_order(order_id, symbol)
            logger.info(f"订单已撤销: {order_id}")
            return True
        except Exception as e:
            logger.error(f"撤销订单失败: {e}")
            return False

    def get_order_status(self, order_id: str, symbol: str) -> Optional[Dict]:
        """获取订单状态"""
        try:
            order = self.exchange.fetch_order(order_id, symbol)
            return order
        except Exception as e:
            logger.error(f"获取订单状态失败: {e}")
            return None

    def close_position(self, symbol: str, side: str = None) -> bool:
        """平仓"""
        try:
            positions = self.get_positions()
            target_position = None

            for pos in positions:
                if pos['symbol'] == symbol:
                    if side is None or pos['side'] == side:
                        target_position = pos
                        break

            if target_position is None:
                logger.warning(f"未找到需要平仓的持仓: {symbol}")
                return False

            # 计算平仓方向和数量
            position_side = target_position['side']
            position_size = abs(float(target_position['size']))

            close_side = 'sell' if position_side == 'long' else 'buy'

            # 执行平仓
            order = self.create_market_order(symbol, close_side, position_size)

            if order:
                logger.info(f"平仓成功: {symbol} {close_side} {position_size}")
                return True
            else:
                return False

        except Exception as e:
            logger.error(f"平仓失败: {e}")
            return False

