"""
CryptoQuant 主入口文件
模块化的量化交易系统主程序

用法示例：
1) 训练模型：
   python main.py --mode train --csv data/BTCUSDT_5m.csv --symbol BTCUSDT --timeframe 5m

2) 回测：
   python main.py --mode backtest --csv data/BTCUSDT_5m.csv --symbol BTCUSDT --timeframe 5m --model_path models/BTCUSDT_5m.pkl

3) 实盘交易（谨慎使用）：
   python main.py --mode live --exchange binanceusdm --symbol BTC/USDT --timeframe 5m --model_path models/BTCUSDT_5m.pkl --api_key YOUR_KEY --api_secret YOUR_SECRET
"""

import argparse
import os
import sys
import json
from typing import Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入模块化组件
from utils.helpers import read_ohlcv_csv
from utils.config import Config, SignalParams
from utils.logger import get_logger

from features.indicators import build_signals
from features.labeling import triple_barrier_labeling, combine_labels
from features.build_features import create_training_dataset

from models.train import train_classifier
from models.save_load import model_manager
from models.evaluate import comprehensive_evaluation

from strategy.run_backtest import run_backtest
from strategy.ml_filter import MLFilterStrategy

# 可选依赖
try:
    import optuna
    from optimization.optuna_objective import create_backtest_objective
except ImportError:
    optuna = None

logger = get_logger("main")


def train_mode(args, config: Config):
    """训练模式"""
    logger.info("=== 训练模式 ===")
    
    # 读取数据
    logger.info(f"读取数据: {args.csv}")
    df = read_ohlcv_csv(args.csv)
    logger.info(f"数据范围: {df.index[0]} 到 {df.index[-1]}, 共 {len(df)} 条记录")
    
    # 创建训练数据集
    logger.info("构建特征和标签...")
    features, labels = create_training_dataset(df, config.signal_params)
    logger.info(f"特征维度: {features.shape}, 标签分布: {labels.value_counts().to_dict()}")
    
    # 训练模型
    logger.info("训练机器学习模型...")
    model = train_classifier(features, labels, config.model_config)
    
    # 保存模型
    model_name = f"{args.symbol}_{args.timeframe}"
    metadata = {
        'symbol': args.symbol,
        'timeframe': args.timeframe,
        'data_points': len(df),
        'feature_count': features.shape[1],
        'positive_labels': int((labels > 0).sum()),
        'negative_labels': int((labels < 0).sum()),
        'neutral_labels': int((labels == 0).sum())
    }
    
    model_path = model_manager.save_model(
        model, model_name, metadata, list(features.columns)
    )
    
    logger.info(f"模型已保存: {model_path}")
    
    # 如果安装了optuna，进行超参数优化
    if optuna and args.study_trials > 0:
        logger.info("开始超参数优化...")
        
        study = optuna.create_study(direction='maximize')
        objective = create_backtest_objective(df, MLFilterStrategy, model_path, config.optimization_config)
        
        study.optimize(objective, n_trials=args.study_trials)
        
        logger.info("优化完成!")
        logger.info(f"最佳Trial: {study.best_trial.number}")
        logger.info(f"最佳分数: {study.best_value:.4f}")
        logger.info("最佳参数:")
        for key, value in study.best_trial.params.items():
            logger.info(f"  {key}: {value}")
        
        # 保存优化结果
        results_file = f"optimization_results_{args.symbol}_{args.timeframe}.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({
                'best_params': study.best_trial.params,
                'best_value': study.best_value,
                'best_trial': study.best_trial.number,
                'n_trials': len(study.trials)
            }, f, indent=2, ensure_ascii=False)
        
        logger.info(f"优化结果已保存: {results_file}")


def backtest_mode(args, config: Config):
    """回测模式"""
    logger.info("=== 回测模式 ===")
    
    # 读取数据
    logger.info(f"读取数据: {args.csv}")
    df = read_ohlcv_csv(args.csv)
    
    # 检查模型文件
    if not args.model_path or not os.path.exists(args.model_path):
        logger.error("模型文件不存在，请先训练模型或提供正确的模型路径")
        return
    
    # 准备回测参数
    params = {
        'ma_s': config.signal_params.ma_s,
        'ma_l': config.signal_params.ma_l,
        'atr_n': config.signal_params.atr_n,
        'tp_atr': config.signal_params.tp_atr,
        'sl_atr': config.signal_params.sl_atr,
        'max_hold_bars': config.signal_params.max_hold_bars,
        'delta': config.signal_params.delta,
        'prob_threshold': config.model_config.prob_threshold,
        'model_path': args.model_path,
        'commission': config.backtest_config.commission,
        'cash_start': config.backtest_config.cash_start,
        'risk_perc': config.backtest_config.risk_perc,
    }
    
    # 运行回测
    logger.info("开始回测...")
    metrics = run_backtest(df, MLFilterStrategy, params)
    
    # 显示结果
    logger.info("回测结果:")
    for key, value in metrics.items():
        if isinstance(value, float):
            if 'rate' in key or 'return' in key:
                logger.info(f"  {key}: {value:.2%}")
            else:
                logger.info(f"  {key}: {value:.4f}")
        else:
            logger.info(f"  {key}: {value}")
    
    # 保存结果
    results_file = f"backtest_results_{args.symbol}_{args.timeframe}.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        # 转换numpy类型为Python原生类型
        serializable_metrics = {}
        for k, v in metrics.items():
            if hasattr(v, 'item'):  # numpy类型
                serializable_metrics[k] = v.item()
            else:
                serializable_metrics[k] = v
        
        json.dump(serializable_metrics, f, indent=2, ensure_ascii=False)
    
    logger.info(f"回测结果已保存: {results_file}")


def live_mode(args, config: Config):
    """实盘模式"""
    logger.info("=== 实盘模式 ===")
    logger.warning("实盘交易有风险，请谨慎操作！")
    
    # 检查必要参数
    if not args.api_key or not args.api_secret:
        logger.error("实盘模式需要提供API密钥")
        return
    
    if not args.model_path or not os.path.exists(args.model_path):
        logger.error("实盘模式需要提供有效的模型文件")
        return
    
    # 导入实盘交易模块
    try:
        from live.live_trade import LiveTrader
    except ImportError as e:
        logger.error(f"导入实盘交易模块失败: {e}")
        return
    
    # 创建实盘交易器
    trader = LiveTrader(
        exchange=config.live_trading_config.exchange,
        api_key=config.live_trading_config.api_key,
        api_secret=config.live_trading_config.api_secret,
        symbol=config.live_trading_config.symbol,
        timeframe=config.live_trading_config.timeframe,
        model_path=args.model_path,
        prob_threshold=config.model_config.prob_threshold,
        ma_s=config.signal_params.ma_s,
        ma_l=config.signal_params.ma_l,
        tp_atr=config.signal_params.tp_atr,
        sl_atr=config.signal_params.sl_atr,
        delta=config.signal_params.delta
    )
    
    logger.info("启动实盘交易...")
    logger.info("按 Ctrl+C 停止交易")
    
    try:
        trader.run()
    except KeyboardInterrupt:
        logger.info("用户停止交易")
    except Exception as e:
        logger.error(f"实盘交易出错: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="CryptoQuant 量化交易系统")
    
    # 基本参数
    parser.add_argument('--mode', required=True, choices=['train', 'backtest', 'live'],
                       help='运行模式')
    parser.add_argument('--config', help='配置文件路径')
    
    # 数据参数
    parser.add_argument('--csv', help='CSV数据文件路径')
    parser.add_argument('--symbol', default='BTCUSDT', help='交易对')
    parser.add_argument('--timeframe', default='5m', help='时间周期')
    
    # 模型参数
    parser.add_argument('--model_path', help='模型文件路径')
    parser.add_argument('--prob_threshold', type=float, default=0.65, help='概率阈值')
    
    # 策略参数
    parser.add_argument('--ma_s', type=int, default=24, help='短期均线周期')
    parser.add_argument('--ma_l', type=int, default=128, help='长期均线周期')
    parser.add_argument('--tp_atr', type=float, default=3.0, help='止盈ATR倍数')
    parser.add_argument('--sl_atr', type=float, default=1.5, help='止损ATR倍数')
    parser.add_argument('--delta', type=float, default=0.5, help='回踩阈值')
    
    # 优化参数
    parser.add_argument('--study_trials', type=int, default=0, help='优化试验次数')
    
    # 实盘参数
    parser.add_argument('--exchange', default='binanceusdm', help='交易所')
    parser.add_argument('--api_key', help='API密钥')
    parser.add_argument('--api_secret', help='API密钥')
    
    args = parser.parse_args()
    
    # 加载配置
    config = Config(args.config)
    config.update_from_args(args)
    
    logger.info("CryptoQuant 量化交易系统启动")
    logger.info(f"运行模式: {args.mode}")
    
    # 根据模式执行相应功能
    if args.mode == 'train':
        if not args.csv:
            logger.error("训练模式需要提供CSV数据文件")
            return
        train_mode(args, config)
    
    elif args.mode == 'backtest':
        if not args.csv:
            logger.error("回测模式需要提供CSV数据文件")
            return
        backtest_mode(args, config)
    
    elif args.mode == 'live':
        live_mode(args, config)
    
    logger.info("程序执行完成")


if __name__ == '__main__':
    main()
