"""
基础策略模块
实现双均线+回踩的基础策略
"""

from __future__ import annotations
import os
import sys
import numpy as np
from typing import Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 可选依赖：根据需要安装
try:
    import backtrader as bt
except ImportError:
    bt = None

from utils.config import SignalParams, BacktestConfig
from utils.logger import get_logger

logger = get_logger("backtest")


class BaseStrategy(bt.Strategy if bt else object):
    """
    基础双均线策略
    实现趋势腿(TL)和回踩腿(PL)信号
    """

    params = dict(
        ma_s=24,           # 短期均线周期
        ma_l=128,          # 长期均线周期
        atr_n=14,          # ATR计算周期
        tp_atr=3.0,        # 止盈ATR倍数
        sl_atr=1.5,        # 止损ATR倍数
        max_hold_bars=24,  # 最大持仓周期
        delta=0.5,         # 回踩触发阈值
        risk_perc=0.003,   # 单笔风险占权益
        cash_start=10000,  # 初始资金
        commission=0.0005, # 手续费（由回测引擎使用）
    )

    def __init__(self):
        if not bt:
            return

        # 价格数据
        self.data_close = self.datas[0].close
        self.data_high = self.datas[0].high
        self.data_low = self.datas[0].low

        # 技术指标
        self.ema_s = bt.ind.EMA(self.data_close, period=self.p.ma_s)
        self.ema_l = bt.ind.EMA(self.data_close, period=self.p.ma_l)
        self.atr = bt.ind.ATR(self.datas[0], period=self.p.atr_n)

        # 方向指标（自定义实现，因为bt.ind.Sign可能不存在）
        # self.dir = bt.ind.Sign(self.ema_s - self.ema_l)
        # 我们在next()方法中动态计算方向

        # 交叉信号
        self.tl_long = bt.ind.CrossOver(self.ema_s, self.ema_l)

        # 交易状态
        self.bar_executed = None
        self.entry_price = None
        self.position_direction = 0  # 1: 多头, -1: 空头, 0: 无持仓

        logger.info("基础策略初始化完成")

    def next(self):
        if not bt:
            return

        # 当前价格和指标
        price = self.data_close[0]
        atr_val = float(self.atr[0]) + 1e-12
        direction = 1 if (self.ema_s[0] - self.ema_l[0]) > 0 else -1

        # 生成信号
        signal = self._generate_signal(price, atr_val, direction)

        if signal != 0:
            self._execute_trade(signal, price, atr_val)

        # 检查退出条件
        self._check_exit_conditions(price, atr_val)

    def _generate_signal(self, price: float, atr_val: float, direction: int) -> int:
        """
        生成交易信号

        Args:
            price: 当前价格
            atr_val: ATR值
            direction: 趋势方向

        Returns:
            信号: 1(买入), -1(卖出), 0(无信号)
        """
        # 趋势腿信号：均线交叉
        tl_signal = 1 if self.tl_long[0] > 0 else (-1 if self.tl_long[0] < 0 else 0)

        # 回踩腿信号：价格回踩到短期均线附近
        distance_normalized = abs(price - self.ema_s[0]) / atr_val
        pl_trigger = 1 if (distance_normalized <= self.p.delta and direction != 0) else 0

        # 信号优先级：趋势腿 > 回踩腿
        if tl_signal != 0:
            return tl_signal
        elif pl_trigger and direction != 0:
            return direction
        else:
            return 0

    def _execute_trade(self, signal: int, price: float, atr_val: float):
        """
        执行交易

        Args:
            signal: 交易信号
            price: 当前价格
            atr_val: ATR值
        """
        # 如果已有持仓，不加仓
        if self.position.size != 0:
            return

        # 计算仓位大小
        size = self._calculate_position_size(atr_val)

        if size <= 0:
            return

        # 执行交易
        if signal > 0:
            self.buy(size=size)
            self.position_direction = 1
            logger.info(f"买入信号: 价格={price:.4f}, 数量={size}")
        elif signal < 0:
            self.sell(size=size)
            self.position_direction = -1
            logger.info(f"卖出信号: 价格={price:.4f}, 数量={size}")

        self.entry_price = price
        self.bar_executed = len(self)

    def _calculate_position_size(self, atr_val: float) -> int:
        """
        计算仓位大小（风险单位法）

        Args:
            atr_val: ATR值

        Returns:
            仓位大小
        """
        cash = self.broker.get_cash()
        risk_amount = cash * self.p.risk_perc
        sl_distance = self.p.sl_atr * atr_val

        if sl_distance <= 0:
            return 0

        size = max(1, int(risk_amount / sl_distance))
        return size

    def _check_exit_conditions(self, price: float, atr_val: float):
        """
        检查退出条件

        Args:
            price: 当前价格
            atr_val: ATR值
        """
        if self.position.size == 0 or self.entry_price is None:
            return

        # 计算止盈止损价位
        tp_price, sl_price = self._calculate_exit_prices(atr_val)

        # 检查止盈止损
        if self._check_tp_sl(price, tp_price, sl_price):
            return

        # 检查时间止损
        if self._check_time_exit():
            return

    def _calculate_exit_prices(self, atr_val: float) -> tuple:
        """
        计算止盈止损价位

        Args:
            atr_val: ATR值

        Returns:
            (止盈价, 止损价)
        """
        if self.position_direction > 0:  # 多头
            tp_price = self.entry_price + self.p.tp_atr * atr_val
            sl_price = self.entry_price - self.p.sl_atr * atr_val
        else:  # 空头
            tp_price = self.entry_price - self.p.tp_atr * atr_val
            sl_price = self.entry_price + self.p.sl_atr * atr_val

        return tp_price, sl_price

    def _check_tp_sl(self, price: float, tp_price: float, sl_price: float) -> bool:
        """
        检查止盈止损

        Args:
            price: 当前价格
            tp_price: 止盈价
            sl_price: 止损价

        Returns:
            是否触发退出
        """
        if self.position_direction > 0:  # 多头
            if price >= tp_price:
                self.close()
                logger.info(f"多头止盈: 价格={price:.4f}, 目标={tp_price:.4f}")
                self._reset_position()
                return True
            elif price <= sl_price:
                self.close()
                logger.info(f"多头止损: 价格={price:.4f}, 目标={sl_price:.4f}")
                self._reset_position()
                return True
        else:  # 空头
            if price <= tp_price:
                self.close()
                logger.info(f"空头止盈: 价格={price:.4f}, 目标={tp_price:.4f}")
                self._reset_position()
                return True
            elif price >= sl_price:
                self.close()
                logger.info(f"空头止损: 价格={price:.4f}, 目标={sl_price:.4f}")
                self._reset_position()
                return True

        return False

    def _check_time_exit(self) -> bool:
        """
        检查时间止损

        Returns:
            是否触发时间退出
        """
        if (self.bar_executed is not None and
            (len(self) - self.bar_executed) >= self.p.max_hold_bars):
            self.close()
            logger.info(f"时间止损: 持仓{len(self) - self.bar_executed}根K线")
            self._reset_position()
            return True

        return False

    def _reset_position(self):
        """重置持仓状态"""
        self.entry_price = None
        self.bar_executed = None
        self.position_direction = 0

