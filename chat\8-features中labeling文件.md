帮你把 `features/labeling.py` 也写出来，让它直接调用这些函数并把结果打包进训练数据。

在 `features/labeling.py` 里，我帮你写一个 **事件打标模块**，它主要是调用 `utils/labeling.py` 中的打标函数，并把结果合并到特征表里。这样能保证解耦：`utils/labeling.py` 专注于“算法逻辑”，而 `features/labeling.py` 负责“和特征表的集成”。

下面是代码：

```python
# features/labeling.py
"""
事件打标模块
---------------------------------
负责调用 utils/labeling.py 中的打标函数，
并与特征表进行合并，方便下游训练使用。
"""

import pandas as pd
from utils.labeling import triple_barrier_labeling, soft_labeling


def apply_triple_barrier(df: pd.DataFrame,
                         pt_sl: tuple = (1, 1),
                         max_holding: int = 20,
                         vol_col: str = "volatility") -> pd.DataFrame:
    """
    对特征表应用三重边界打标

    Parameters
    ----------
    df : pd.DataFrame
        必须包含 ['close', vol_col] 列
    pt_sl : tuple
        (止盈倍数, 止损倍数)
    max_holding : int
        最大持仓周期
    vol_col : str
        波动率列名（通常由预处理或特征工程计算）

    Returns
    -------
    pd.DataFrame
        带有 'triple_barrier_label' 列的新表
    """
    labels = triple_barrier_labeling(df, pt_sl=pt_sl,
                                     max_holding=max_holding,
                                     vol_col=vol_col)
    df = df.copy()
    df["triple_barrier_label"] = labels
    return df


def apply_soft_labeling(df: pd.DataFrame,
                        horizon: int = 20) -> pd.DataFrame:
    """
    对特征表应用软标签打标

    Parameters
    ----------
    df : pd.DataFrame
        必须包含 'close' 列
    horizon : int
        向前看的周期数

    Returns
    -------
    pd.DataFrame
        带有 'soft_label' 和 'future_return' 列的新表
    """
    labels, fut_ret = soft_labeling(df, horizon=horizon)
    df = df.copy()
    df["soft_label"] = labels
    df["future_return"] = fut_ret
    return df


def add_labels(df: pd.DataFrame,
               use_soft: bool = True,
               use_triple_barrier: bool = False,
               vol_col: str = "volatility",
               horizon: int = 20,
               pt_sl: tuple = (1, 1),
               max_holding: int = 20) -> pd.DataFrame:
    """
    主入口：根据参数选择打标方式，并整合到特征表

    Parameters
    ----------
    df : pd.DataFrame
        特征表
    use_soft : bool
        是否添加软标签
    use_triple_barrier : bool
        是否添加三重边界标签
    vol_col : str
        波动率列名
    horizon : int
        软标签 horizon
    pt_sl : tuple
        三重边界止盈止损倍数
    max_holding : int
        三重边界最大持仓周期

    Returns
    -------
    pd.DataFrame
        带标签的特征表
    """
    df = df.copy()

    if use_soft:
        df = apply_soft_labeling(df, horizon=horizon)

    if use_triple_barrier:
        df = apply_triple_barrier(df,
                                  pt_sl=pt_sl,
                                  max_holding=max_holding,
                                  vol_col=vol_col)
    return df
```

这样设计的好处是：

* 你只需要在 **训练特征表构建的时候** 调用 `add_labels(df, use_soft=True, use_triple_barrier=True)`
* 下游模型就能同时获得 **分类标签** 和 **回归目标**，还能选择性使用 **三重边界**。

---

要不要我帮你在 `features/build_features.py` 里把 `add_labels()` 集成进去，直接生成 **X + y**（特征矩阵和标签），方便训练？
