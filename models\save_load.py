"""
模型保存和加载模块
支持模型的持久化存储和加载
"""

from __future__ import annotations
import os
import json
import pickle
from datetime import datetime
from typing import Optional, Dict, Any
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import joblib
import pandas as pd
from sklearn.pipeline import Pipeline
from utils.logger import get_logger

logger = get_logger("main")


class ModelManager:
    """模型管理器"""

    def __init__(self, models_dir: str = "models"):
        self.models_dir = models_dir
        os.makedirs(models_dir, exist_ok=True)

    def save_model(self, model: Pipeline, model_name: str,
                   metadata: Optional[Dict[str, Any]] = None,
                   feature_names: Optional[list] = None) -> str:
        """
        保存模型

        Args:
            model: 训练好的模型
            model_name: 模型名称
            metadata: 模型元数据
            feature_names: 特征名称列表

        Returns:
            保存路径
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_filename = f"{model_name}_{timestamp}.pkl"
        model_path = os.path.join(self.models_dir, model_filename)

        # 保存模型
        joblib.dump(model, model_path)
        logger.info(f"模型已保存到: {model_path}")

        # 保存元数据
        if metadata is not None or feature_names is not None:
            metadata_dict = metadata or {}
            metadata_dict.update({
                'model_name': model_name,
                'save_time': timestamp,
                'model_file': model_filename,
                'feature_names': feature_names
            })

            metadata_filename = f"{model_name}_{timestamp}_metadata.json"
            metadata_path = os.path.join(self.models_dir, metadata_filename)

            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata_dict, f, indent=2, ensure_ascii=False)

            logger.info(f"元数据已保存到: {metadata_path}")

        return model_path

    def load_model(self, model_path: str) -> Pipeline:
        """
        加载模型

        Args:
            model_path: 模型路径

        Returns:
            加载的模型
        """
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")

        model = joblib.load(model_path)
        logger.info(f"模型已从 {model_path} 加载")

        return model

    def load_metadata(self, model_path: str) -> Optional[Dict[str, Any]]:
        """
        加载模型元数据

        Args:
            model_path: 模型路径

        Returns:
            元数据字典
        """
        # 构造元数据文件路径
        base_name = os.path.splitext(os.path.basename(model_path))[0]
        metadata_filename = f"{base_name}_metadata.json"
        metadata_path = os.path.join(os.path.dirname(model_path), metadata_filename)

        if not os.path.exists(metadata_path):
            logger.warning(f"元数据文件不存在: {metadata_path}")
            return None

        with open(metadata_path, 'r', encoding='utf-8') as f:
            metadata = json.load(f)

        logger.info(f"元数据已从 {metadata_path} 加载")
        return metadata

    def list_models(self) -> pd.DataFrame:
        """
        列出所有保存的模型

        Returns:
            模型列表DataFrame
        """
        model_files = [f for f in os.listdir(self.models_dir) if f.endswith('.pkl')]

        models_info = []
        for model_file in model_files:
            model_path = os.path.join(self.models_dir, model_file)
            file_stat = os.stat(model_path)

            # 尝试加载元数据
            metadata = self.load_metadata(model_path)

            info = {
                'filename': model_file,
                'path': model_path,
                'size_mb': file_stat.st_size / (1024 * 1024),
                'modified_time': datetime.fromtimestamp(file_stat.st_mtime),
                'model_name': metadata.get('model_name', 'Unknown') if metadata else 'Unknown',
                'save_time': metadata.get('save_time', 'Unknown') if metadata else 'Unknown'
            }
            models_info.append(info)

        return pd.DataFrame(models_info)

    def delete_model(self, model_path: str) -> bool:
        """
        删除模型文件

        Args:
            model_path: 模型路径

        Returns:
            是否删除成功
        """
        try:
            if os.path.exists(model_path):
                os.remove(model_path)
                logger.info(f"模型文件已删除: {model_path}")

                # 尝试删除对应的元数据文件
                base_name = os.path.splitext(os.path.basename(model_path))[0]
                metadata_filename = f"{base_name}_metadata.json"
                metadata_path = os.path.join(os.path.dirname(model_path), metadata_filename)

                if os.path.exists(metadata_path):
                    os.remove(metadata_path)
                    logger.info(f"元数据文件已删除: {metadata_path}")

                return True
            else:
                logger.warning(f"模型文件不存在: {model_path}")
                return False
        except Exception as e:
            logger.error(f"删除模型文件失败: {e}")
            return False


def save_model_simple(model: Pipeline, filepath: str) -> None:
    """
    简单保存模型（兼容原始代码）

    Args:
        model: 训练好的模型
        filepath: 保存路径
    """
    # 确保目录存在
    os.makedirs(os.path.dirname(filepath), exist_ok=True)

    joblib.dump(model, filepath)
    logger.info(f"模型已保存到: {filepath}")


def load_model_simple(filepath: str) -> Pipeline:
    """
    简单加载模型（兼容原始代码）

    Args:
        filepath: 模型路径

    Returns:
        加载的模型
    """
    if not os.path.exists(filepath):
        raise FileNotFoundError(f"模型文件不存在: {filepath}")

    model = joblib.load(filepath)
    logger.info(f"模型已从 {filepath} 加载")

    return model


def backup_model(model_path: str, backup_dir: str = "model_backups") -> str:
    """
    备份模型

    Args:
        model_path: 原模型路径
        backup_dir: 备份目录

    Returns:
        备份路径
    """
    import shutil

    os.makedirs(backup_dir, exist_ok=True)

    filename = os.path.basename(model_path)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_filename = f"backup_{timestamp}_{filename}"
    backup_path = os.path.join(backup_dir, backup_filename)

    shutil.copy2(model_path, backup_path)
    logger.info(f"模型已备份到: {backup_path}")

    return backup_path


# 全局模型管理器实例
model_manager = ModelManager()

