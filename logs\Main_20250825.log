2025-08-25 10:32:05 - Main - INFO - CryptoQuant 量化交易系统启动
2025-08-25 10:32:05 - Main - INFO - 运行模式: train
2025-08-25 10:32:05 - Main - INFO - === 训练模式 ===
2025-08-25 10:32:05 - Main - INFO - 读取数据: data/ETHUSDT_5m.csv
2025-08-25 10:32:05 - Main - INFO - 数据范围: 2023-12-31 16:00:00 到 2025-01-01 15:55:00, 共 105696 条记录
2025-08-25 10:32:05 - Main - INFO - 构建特征和标签...
2025-08-25 10:32:11 - Main - INFO - 特征维度: (105596, 33), 标签分布: {0.0: 79054, -1.0: 17746, 1.0: 8796}
2025-08-25 10:32:11 - Main - INFO - 训练机器学习模型...
2025-08-25 10:32:11 - Main - INFO - 开始训练模型: GradientBoostingClassifier
2025-08-25 10:32:11 - Main - INFO - 特征数量: 33, 样本数量: 105596
2025-08-25 10:32:11 - Main - INFO - 正类样本: 8796, 负类样本: 96800
2025-08-25 10:32:11 - Main - INFO - 训练折 1/5
2025-08-25 10:32:27 - Main - INFO - 折 1 AUC: 0.8650
2025-08-25 10:32:27 - Main - INFO - 训练折 2/5
2025-08-25 10:33:00 - Main - INFO - 折 2 AUC: 0.8743
2025-08-25 10:33:00 - Main - INFO - 训练折 3/5
2025-08-25 10:33:51 - Main - INFO - 折 3 AUC: 0.8826
2025-08-25 10:33:51 - Main - INFO - 训练折 4/5
2025-08-25 10:35:02 - Main - INFO - 折 4 AUC: 0.8911
2025-08-25 10:35:02 - Main - INFO - 训练折 5/5
2025-08-25 10:36:32 - Main - INFO - 折 5 AUC: 0.8788
2025-08-25 10:36:32 - Main - INFO - 交叉验证 AUC: 0.8784 ± 0.0087
2025-08-25 10:36:32 - Main - INFO - 使用全部数据进行最终训练
2025-08-25 10:38:30 - Main - INFO - 模型已保存到: models\ETHUSDT_5m_20250825_103830.pkl
2025-08-25 10:38:30 - Main - INFO - 元数据已保存到: models\ETHUSDT_5m_20250825_103830_metadata.json
2025-08-25 10:38:30 - Main - INFO - 模型已保存: models\ETHUSDT_5m_20250825_103830.pkl
2025-08-25 10:38:30 - Main - INFO - 开始超参数优化...
2025-08-25 10:38:41 - Main - ERROR - Trial 0 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:38:50 - Main - ERROR - Trial 1 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:39:00 - Main - ERROR - Trial 2 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:39:09 - Main - ERROR - Trial 3 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:39:19 - Main - ERROR - Trial 4 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:39:28 - Main - ERROR - Trial 5 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:39:37 - Main - ERROR - Trial 6 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:39:47 - Main - ERROR - Trial 7 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:39:56 - Main - ERROR - Trial 8 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:40:06 - Main - ERROR - Trial 9 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:40:16 - Main - ERROR - Trial 10 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:40:26 - Main - ERROR - Trial 11 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:40:35 - Main - ERROR - Trial 12 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:40:45 - Main - ERROR - Trial 13 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:40:54 - Main - ERROR - Trial 14 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:41:03 - Main - ERROR - Trial 15 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:41:12 - Main - ERROR - Trial 16 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:41:22 - Main - ERROR - Trial 17 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:41:31 - Main - ERROR - Trial 18 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:41:40 - Main - ERROR - Trial 19 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:41:50 - Main - ERROR - Trial 20 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:42:00 - Main - ERROR - Trial 21 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:42:10 - Main - ERROR - Trial 22 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:42:20 - Main - ERROR - Trial 23 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:42:29 - Main - ERROR - Trial 24 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:42:39 - Main - ERROR - Trial 25 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:42:49 - Main - ERROR - Trial 26 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:42:59 - Main - ERROR - Trial 27 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:43:09 - Main - ERROR - Trial 28 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:43:19 - Main - ERROR - Trial 29 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:43:28 - Main - ERROR - Trial 30 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:43:38 - Main - ERROR - Trial 31 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:43:48 - Main - ERROR - Trial 32 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:43:59 - Main - ERROR - Trial 33 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:44:10 - Main - ERROR - Trial 34 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:44:19 - Main - ERROR - Trial 35 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:44:29 - Main - ERROR - Trial 36 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:44:40 - Main - ERROR - Trial 37 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:44:50 - Main - ERROR - Trial 38 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:45:00 - Main - ERROR - Trial 39 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:45:12 - Main - ERROR - Trial 40 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:45:23 - Main - ERROR - Trial 41 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:45:33 - Main - ERROR - Trial 42 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:45:43 - Main - ERROR - Trial 43 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:45:53 - Main - ERROR - Trial 44 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:46:03 - Main - ERROR - Trial 45 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:46:13 - Main - ERROR - Trial 46 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:46:23 - Main - ERROR - Trial 47 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:46:33 - Main - ERROR - Trial 48 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:46:43 - Main - ERROR - Trial 49 失败: MLFilterStrategy.__init__() got an unexpected keyword argument 'commission'
2025-08-25 10:46:43 - Main - INFO - 优化完成!
2025-08-25 10:46:43 - Main - INFO - 最佳Trial: 0
2025-08-25 10:46:43 - Main - INFO - 最佳分数: -inf
2025-08-25 10:46:43 - Main - INFO - 最佳参数:
2025-08-25 10:46:43 - Main - INFO -   ma_s: 15
2025-08-25 10:46:43 - Main - INFO -   ma_l: 144
2025-08-25 10:46:43 - Main - INFO -   tp_atr: 2.325641925637932
2025-08-25 10:46:43 - Main - INFO -   sl_atr: 0.8216878862934187
2025-08-25 10:46:43 - Main - INFO -   delta: 0.4112575108816488
2025-08-25 10:46:43 - Main - INFO -   prob_threshold: 0.6807927384324723
2025-08-25 10:46:43 - Main - INFO - 优化结果已保存: optimization_results_ETHUSDT_5m.json
2025-08-25 10:46:43 - Main - INFO - 程序执行完成
2025-08-25 11:40:45 - Main - INFO - CryptoQuant 量化交易系统启动
2025-08-25 11:40:45 - Main - INFO - 运行模式: train
2025-08-25 11:40:45 - Main - INFO - === 训练模式 ===
2025-08-25 11:40:45 - Main - INFO - 读取数据: data/ETHUSDT_5m.csv
2025-08-25 11:40:45 - Main - INFO - 数据范围: 2023-12-31 16:00:00 到 2025-01-01 15:55:00, 共 105696 条记录
2025-08-25 11:40:45 - Main - INFO - 构建特征和标签...
2025-08-25 11:40:51 - Main - INFO - 特征维度: (105596, 33), 标签分布: {0.0: 79054, -1.0: 17746, 1.0: 8796}
2025-08-25 11:40:51 - Main - INFO - 训练机器学习模型...
2025-08-25 11:40:51 - Main - INFO - 开始训练模型: GradientBoostingClassifier
2025-08-25 11:40:51 - Main - INFO - 特征数量: 33, 样本数量: 105596
2025-08-25 11:40:51 - Main - INFO - 正类样本: 8796, 负类样本: 96800
2025-08-25 11:40:51 - Main - INFO - 训练折 1/5
2025-08-25 11:41:07 - Main - INFO - 折 1 AUC: 0.8650
2025-08-25 11:41:07 - Main - INFO - 训练折 2/5
2025-08-25 11:41:42 - Main - INFO - 折 2 AUC: 0.8743
2025-08-25 11:41:42 - Main - INFO - 训练折 3/5
2025-08-25 11:42:37 - Main - INFO - 折 3 AUC: 0.8826
2025-08-25 11:42:37 - Main - INFO - 训练折 4/5
2025-08-25 11:43:51 - Main - INFO - 折 4 AUC: 0.8911
2025-08-25 11:43:51 - Main - INFO - 训练折 5/5
2025-08-25 11:45:32 - Main - INFO - 折 5 AUC: 0.8788
2025-08-25 11:45:32 - Main - INFO - 交叉验证 AUC: 0.8784 ± 0.0087
2025-08-25 11:45:32 - Main - INFO - 使用全部数据进行最终训练
2025-08-25 11:47:37 - Main - INFO - 模型已保存到: models\ETHUSDT_5m_20250825_114737.pkl
2025-08-25 11:47:37 - Main - INFO - 元数据已保存到: models\ETHUSDT_5m_20250825_114737_metadata.json
2025-08-25 11:47:37 - Main - INFO - 模型已保存: models\ETHUSDT_5m_20250825_114737.pkl
2025-08-25 11:47:37 - Main - INFO - 开始超参数优化...
2025-08-25 11:47:47 - Main - ERROR - Trial 0 失败: module 'backtrader.indicators' has no attribute 'Sign'
2025-08-25 11:47:58 - Main - ERROR - Trial 1 失败: module 'backtrader.indicators' has no attribute 'Sign'
2025-08-25 11:47:58 - Main - INFO - 优化完成!
2025-08-25 11:47:58 - Main - INFO - 最佳Trial: 0
2025-08-25 11:47:58 - Main - INFO - 最佳分数: -inf
2025-08-25 11:47:58 - Main - INFO - 最佳参数:
2025-08-25 11:47:58 - Main - INFO -   ma_s: 25
2025-08-25 11:47:58 - Main - INFO -   ma_l: 222
2025-08-25 11:47:58 - Main - INFO -   tp_atr: 2.308753518356022
2025-08-25 11:47:58 - Main - INFO -   sl_atr: 1.0668435119630697
2025-08-25 11:47:58 - Main - INFO -   delta: 0.6216994974692375
2025-08-25 11:47:58 - Main - INFO -   prob_threshold: 0.5398204570060297
2025-08-25 11:47:58 - Main - INFO - 优化结果已保存: optimization_results_ETHUSDT_5m.json
2025-08-25 11:47:58 - Main - INFO - 程序执行完成
2025-08-25 11:49:29 - Main - INFO - CryptoQuant 量化交易系统启动
2025-08-25 11:49:29 - Main - INFO - 运行模式: train
2025-08-25 11:49:29 - Main - INFO - === 训练模式 ===
2025-08-25 11:49:29 - Main - INFO - 读取数据: data/ETHUSDT_5m.csv
2025-08-25 11:49:29 - Main - INFO - 数据范围: 2023-12-31 16:00:00 到 2025-01-01 15:55:00, 共 105696 条记录
2025-08-25 11:49:29 - Main - INFO - 构建特征和标签...
2025-08-25 11:49:36 - Main - INFO - 特征维度: (105596, 33), 标签分布: {0.0: 79054, -1.0: 17746, 1.0: 8796}
2025-08-25 11:49:36 - Main - INFO - 训练机器学习模型...
2025-08-25 11:49:36 - Main - INFO - 开始训练模型: GradientBoostingClassifier
2025-08-25 11:49:36 - Main - INFO - 特征数量: 33, 样本数量: 105596
2025-08-25 11:49:36 - Main - INFO - 正类样本: 8796, 负类样本: 96800
2025-08-25 11:49:36 - Main - INFO - 训练折 1/5
2025-08-25 11:49:53 - Main - INFO - 折 1 AUC: 0.8650
2025-08-25 11:49:53 - Main - INFO - 训练折 2/5
2025-08-25 11:50:29 - Main - INFO - 折 2 AUC: 0.8743
2025-08-25 11:50:29 - Main - INFO - 训练折 3/5
2025-08-25 11:51:24 - Main - INFO - 折 3 AUC: 0.8826
2025-08-25 11:51:24 - Main - INFO - 训练折 4/5
2025-08-25 11:52:41 - Main - INFO - 折 4 AUC: 0.8911
2025-08-25 11:52:41 - Main - INFO - 训练折 5/5
2025-08-25 11:54:16 - Main - INFO - 折 5 AUC: 0.8788
2025-08-25 11:54:16 - Main - INFO - 交叉验证 AUC: 0.8784 ± 0.0087
2025-08-25 11:54:16 - Main - INFO - 使用全部数据进行最终训练
2025-08-25 11:56:09 - Main - INFO - 模型已保存到: models\ETHUSDT_5m_20250825_115609.pkl
2025-08-25 11:56:09 - Main - INFO - 元数据已保存到: models\ETHUSDT_5m_20250825_115609_metadata.json
2025-08-25 11:56:09 - Main - INFO - 模型已保存: models\ETHUSDT_5m_20250825_115609.pkl
2025-08-25 11:56:09 - Main - INFO - 开始超参数优化...
2025-08-25 11:58:31 - Main - INFO - CryptoQuant 量化交易系统启动
2025-08-25 11:58:31 - Main - INFO - 运行模式: train
2025-08-25 11:58:31 - Main - INFO - === 训练模式 ===
2025-08-25 11:58:31 - Main - INFO - 读取数据: data/ETHUSDT_5m.csv
2025-08-25 11:58:31 - Main - INFO - 数据范围: 2023-12-31 16:00:00 到 2025-01-01 15:55:00, 共 105696 条记录
2025-08-25 11:58:31 - Main - INFO - 构建特征和标签...
2025-08-25 11:58:38 - Main - INFO - 特征维度: (105596, 33), 标签分布: {0.0: 79054, -1.0: 17746, 1.0: 8796}
2025-08-25 11:58:38 - Main - INFO - 训练机器学习模型...
2025-08-25 11:58:38 - Main - INFO - 开始训练模型: GradientBoostingClassifier
2025-08-25 11:58:38 - Main - INFO - 特征数量: 33, 样本数量: 105596
2025-08-25 11:58:38 - Main - INFO - 正类样本: 8796, 负类样本: 96800
2025-08-25 11:58:38 - Main - INFO - 训练折 1/5
2025-08-25 11:58:55 - Main - INFO - 折 1 AUC: 0.8650
2025-08-25 11:58:55 - Main - INFO - 训练折 2/5
2025-08-25 11:59:30 - Main - INFO - 折 2 AUC: 0.8743
2025-08-25 11:59:30 - Main - INFO - 训练折 3/5
2025-08-25 12:00:26 - Main - INFO - 折 3 AUC: 0.8826
2025-08-25 12:00:26 - Main - INFO - 训练折 4/5
2025-08-25 12:01:41 - Main - INFO - 折 4 AUC: 0.8911
2025-08-25 12:01:41 - Main - INFO - 训练折 5/5
2025-08-25 12:03:13 - Main - INFO - 折 5 AUC: 0.8788
2025-08-25 12:03:13 - Main - INFO - 交叉验证 AUC: 0.8784 ± 0.0087
2025-08-25 12:03:13 - Main - INFO - 使用全部数据进行最终训练
2025-08-25 12:05:07 - Main - INFO - 模型已保存到: models\ETHUSDT_5m_20250825_120507.pkl
2025-08-25 12:05:07 - Main - INFO - 元数据已保存到: models\ETHUSDT_5m_20250825_120507_metadata.json
2025-08-25 12:05:07 - Main - INFO - 模型已保存: models\ETHUSDT_5m_20250825_120507.pkl
2025-08-25 12:05:07 - Main - INFO - 开始超参数优化...
2025-08-25 12:06:32 - Main - INFO - CryptoQuant 量化交易系统启动
2025-08-25 12:06:32 - Main - INFO - 运行模式: train
2025-08-25 12:06:32 - Main - INFO - === 训练模式 ===
2025-08-25 12:06:32 - Main - INFO - 读取数据: data/ETHUSDT_5m.csv
2025-08-25 12:06:32 - Main - INFO - 数据范围: 2023-12-31 16:00:00 到 2025-01-01 15:55:00, 共 105696 条记录
2025-08-25 12:06:32 - Main - INFO - 构建特征和标签...
2025-08-25 12:06:38 - Main - INFO - 特征维度: (105596, 33), 标签分布: {0.0: 79054, -1.0: 17746, 1.0: 8796}
2025-08-25 12:06:38 - Main - INFO - 训练机器学习模型...
2025-08-25 12:06:38 - Main - INFO - 开始训练模型: GradientBoostingClassifier
2025-08-25 12:06:38 - Main - INFO - 特征数量: 33, 样本数量: 105596
2025-08-25 12:06:38 - Main - INFO - 正类样本: 8796, 负类样本: 96800
2025-08-25 12:06:38 - Main - INFO - 训练折 1/5
2025-08-25 12:06:54 - Main - INFO - 折 1 AUC: 0.8650
2025-08-25 12:06:54 - Main - INFO - 训练折 2/5
2025-08-25 12:07:29 - Main - INFO - 折 2 AUC: 0.8743
2025-08-25 12:07:29 - Main - INFO - 训练折 3/5
2025-08-25 12:08:23 - Main - INFO - 折 3 AUC: 0.8826
2025-08-25 12:08:23 - Main - INFO - 训练折 4/5
2025-08-25 12:09:37 - Main - INFO - 折 4 AUC: 0.8911
2025-08-25 12:09:37 - Main - INFO - 训练折 5/5
2025-08-25 12:11:12 - Main - INFO - 折 5 AUC: 0.8788
2025-08-25 12:11:12 - Main - INFO - 交叉验证 AUC: 0.8784 ± 0.0087
2025-08-25 12:11:12 - Main - INFO - 使用全部数据进行最终训练
2025-08-25 12:13:09 - Main - INFO - 模型已保存到: models\ETHUSDT_5m_20250825_121309.pkl
2025-08-25 12:13:09 - Main - INFO - 元数据已保存到: models\ETHUSDT_5m_20250825_121309_metadata.json
2025-08-25 12:13:09 - Main - INFO - 模型已保存: models\ETHUSDT_5m_20250825_121309.pkl
2025-08-25 12:13:09 - Main - INFO - 程序执行完成
2025-08-25 12:13:29 - Main - INFO - CryptoQuant 量化交易系统启动
2025-08-25 12:13:29 - Main - INFO - 运行模式: backtest
2025-08-25 12:13:29 - Main - INFO - === 回测模式 ===
2025-08-25 12:13:29 - Main - INFO - 读取数据: data/ETHUSDT_5m.csv
2025-08-25 12:13:29 - Main - INFO - 开始回测...
2025-08-25 12:14:08 - Main - INFO - 回测结果:
2025-08-25 12:14:08 - Main - INFO -   total_return: -102.54%
2025-08-25 12:14:08 - Main - INFO -   final_value: -253.8472
2025-08-25 12:14:08 - Main - INFO -   sharpe_ratio: -0.6625
2025-08-25 12:14:08 - Main - INFO -   max_drawdown: 1.0985
2025-08-25 12:14:08 - Main - INFO -   total_trades: 1687
2025-08-25 12:14:08 - Main - INFO -   win_rate: 34.44%
2025-08-25 12:14:08 - Main - INFO -   won_trades: 581
2025-08-25 12:14:08 - Main - INFO -   lost_trades: 1105
2025-08-25 12:14:08 - Main - INFO -   sqn: -9.2168
2025-08-25 12:14:08 - Main - INFO - 回测结果已保存: backtest_results_ETHUSDT_5m.json
2025-08-25 12:14:08 - Main - INFO - 程序执行完成
2025-08-25 12:53:27 - Main - INFO - CryptoQuant 量化交易系统启动
2025-08-25 12:53:27 - Main - INFO - 运行模式: train
2025-08-25 12:53:27 - Main - INFO - === 训练模式 ===
2025-08-25 12:53:27 - Main - INFO - 读取数据: data/ETHUSDT_5m.csv
2025-08-25 12:53:27 - Main - INFO - 数据范围: 2023-12-31 16:00:00 到 2025-01-01 15:55:00, 共 105696 条记录
2025-08-25 12:53:27 - Main - INFO - 构建特征和标签...
2025-08-25 12:53:34 - Main - INFO - 特征维度: (105596, 33), 标签分布: {0.0: 79054, -1.0: 17746, 1.0: 8796}
2025-08-25 12:53:34 - Main - INFO - 训练机器学习模型...
2025-08-25 12:53:34 - Main - INFO - 开始训练模型: GradientBoostingClassifier
2025-08-25 12:53:34 - Main - INFO - 特征数量: 33, 样本数量: 105596
2025-08-25 12:53:34 - Main - INFO - 正类样本: 8796, 负类样本: 96800
2025-08-25 12:53:34 - Main - INFO - 训练折 1/5
2025-08-25 12:53:50 - Main - INFO - 折 1 AUC: 0.8650
2025-08-25 12:53:50 - Main - INFO - 训练折 2/5
2025-08-25 12:54:25 - Main - INFO - 折 2 AUC: 0.8743
2025-08-25 12:54:25 - Main - INFO - 训练折 3/5
2025-08-25 12:55:19 - Main - INFO - 折 3 AUC: 0.8826
2025-08-25 12:55:19 - Main - INFO - 训练折 4/5
2025-08-25 12:56:32 - Main - INFO - 折 4 AUC: 0.8911
2025-08-25 12:56:32 - Main - INFO - 训练折 5/5
2025-08-25 12:58:06 - Main - INFO - 折 5 AUC: 0.8788
2025-08-25 12:58:06 - Main - INFO - 交叉验证 AUC: 0.8784 ± 0.0087
2025-08-25 12:58:06 - Main - INFO - 使用全部数据进行最终训练
2025-08-25 13:00:18 - Main - INFO - 模型已保存到: models\ETHUSDT_5m_20250825_130018.pkl
2025-08-25 13:00:18 - Main - INFO - 元数据已保存到: models\ETHUSDT_5m_20250825_130018_metadata.json
2025-08-25 13:00:18 - Main - INFO - 模型已保存: models\ETHUSDT_5m_20250825_130018.pkl
2025-08-25 13:00:18 - Main - INFO - 开始超参数优化...
2025-08-25 13:14:29 - Main - INFO - Trial 0: Score=-1.5821, Return=0.91%, Sharpe=-1.6313, DD=0.34%
2025-08-25 13:28:05 - Main - INFO - Trial 1: Score=0.0000, Return=0.00%, Sharpe=0.0000, DD=0.00%
2025-08-25 14:06:40 - Main - INFO - Trial 2: Score=0.0000, Return=0.00%, Sharpe=0.0000, DD=0.00%
2025-08-25 14:25:01 - Main - INFO - Trial 3: Score=-0.1574, Return=2.32%, Sharpe=-0.2066, DD=0.62%
2025-08-25 14:30:33 - Main - INFO - Trial 4: Score=-1.3470, Return=1.01%, Sharpe=-1.3947, DD=0.67%
