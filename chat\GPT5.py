"""
Backtrader + Optuna + scikit-learn + ccxt
端到端脚手架（双均线趋势腿TL + 回踩腿PL + ML过滤 + 参数优化 + 实盘骨架）

用法（建议新建虚拟环境后安装依赖: backtrader, optuna, scikit-learn, pandas, numpy, matplotlib, ccxt）
--------------------------------------------------------------------------------------
1) 准备数据：
   - 准备 1m 或 5m 的 OHLCV CSV，至少包含列：timestamp, open, high, low, close, volume
   - 时间戳单位为秒或毫秒（会自动判断）

2) 训练与回测：
   python bt_ma_ml_pipeline.py --mode train \
       --csv data/BTCUSDT_5m.csv \
       --symbol BTCUSDT --timeframe 5m \
       --study_trials 50 --oos_months 1

   说明：
   - 会进行三重边界打标、特征工程、训练 sklearn 分类器（可修改模型），
     并用 Optuna 搜索 (ma_s, ma_l, tp_atr, sl_atr, prob_threshold, delta 等)。
   - 输出最佳参数与指标，并将模型保存到 ./models 目录。

3) 仅回测（载入已训练模型进行过滤）：
   python bt_ma_ml_pipeline.py --mode backtest \
       --csv data/BTCUSDT_5m.csv --symbol BTCUSDT --timeframe 5m \
       --model_path models/BTCUSDT_5m.pkl --prob_threshold 0.65 \
       --ma_s 24 --ma_l 128 --tp_atr 3.0 --sl_atr 1.5 --delta 0.5

4) 实盘骨架（ccxt，**谨慎**）：
   python bt_ma_ml_pipeline.py --mode live \
       --exchange binanceusdm --symbol BTC/USDT --timeframe 5m \
       --model_path models/BTCUSDT_5m.pkl --prob_threshold 0.65 \
       --api_key ... --api_secret ...

警告：本脚手架仅用于研究与教学。实盘有风险。请加入严格风控与小额试运行。
--------------------------------------------------------------------------------------
"""

from __future__ import annotations
import os
import math
import argparse
import time
import json
from dataclasses import dataclass
from typing import Optional, Tuple, Dict, Any

import numpy as np
import pandas as pd

# 可选依赖：根据需要安装
try:
    import backtrader as bt
except Exception:
    bt = None

try:
    import ccxt
except Exception:
    ccxt = None

try:
    import optuna
except Exception:
    optuna = None

from sklearn.ensemble import GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline
from sklearn.metrics import roc_auc_score
from sklearn.model_selection import TimeSeriesSplit
import joblib

# =========================
# 数据与指标工具
# =========================

def read_ohlcv_csv(path: str) -> pd.DataFrame:
    """
    读取OHLCV CSV文件，支持多种格式：
    1. 标准格式：timestamp, open, high, low, close, volume
    2. Binance格式：open_time, open, high, low, close, volume, close_time, ...
    """
    df = pd.read_csv(path)

    # 检查是否是Binance格式（包含open_time列）
    if 'open_time' in df.columns:
        # Binance格式处理
        df['datetime'] = pd.to_datetime(df['open_time'])
        df = df.set_index('datetime').sort_index()
        return df[["open", "high", "low", "close", "volume"]].astype(float)

    # 标准格式处理
    cols = {c.lower(): c for c in df.columns}
    for key in ["timestamp", "open", "high", "low", "close", "volume"]:
        assert key in {k.lower() for k in df.columns}, f"CSV 缺少列: {key}"

    # 统一列名
    df = df.rename(columns={cols.get("timestamp"): "timestamp",
                            cols.get("open"): "open",
                            cols.get("high"): "high",
                            cols.get("low"): "low",
                            cols.get("close"): "close",
                            cols.get("volume"): "volume"})

    # 时间戳转 datetime
    ts = df["timestamp"].values
    # 判断毫秒还是秒
    if np.nanmax(ts) > 1e12:
        df["datetime"] = pd.to_datetime(df["timestamp"], unit="ms", utc=True)
    else:
        df["datetime"] = pd.to_datetime(df["timestamp"], unit="s", utc=True)
    df = df.set_index("datetime").sort_index()
    return df[["open", "high", "low", "close", "volume"]].astype(float)


def ema(series: pd.Series, n: int) -> pd.Series:
    return series.ewm(span=n, adjust=False).mean()


def atr(df: pd.DataFrame, n: int = 14) -> pd.Series:
    high, low, close = df["high"], df["low"], df["close"].shift(1)
    tr = np.maximum(high - low, np.maximum((high - close).abs(), (low - close).abs()))
    return tr.ewm(span=n, adjust=False).mean()


def cross_over(a: pd.Series, b: pd.Series) -> pd.Series:
    return (a > b) & (a.shift(1) <= b.shift(1))


def cross_under(a: pd.Series, b: pd.Series) -> pd.Series:
    return (a < b) & (a.shift(1) >= b.shift(1))


# =========================
# 信号与三重边界标注
# =========================

@dataclass
class SignalParams:
    ma_s: int = 24
    ma_l: int = 128
    atr_n: int = 14
    tp_atr: float = 3.0
    sl_atr: float = 1.5
    max_hold_bars: int = 24
    delta: float = 0.5  # 回踩触发阈值: |price - ema_s|/ATR <= delta


def build_signals(df: pd.DataFrame, p: SignalParams) -> pd.DataFrame:
    out = df.copy()
    out["ema_s"] = ema(out["close"], p.ma_s)
    out["ema_l"] = ema(out["close"], p.ma_l)
    out["atr"] = atr(out, p.atr_n)
    out["dir"] = np.sign(out["ema_s"] - out["ema_l"]).fillna(0)
    out["tl_long"] = cross_over(out["ema_s"], out["ema_l"]).astype(int)
    out["tl_short"] = cross_under(out["ema_s"], out["ema_l"]).astype(int)
    # 回踩：在有方向时，z = |price-ema_s|/ATR <= delta
    z = (out["close"] - out["ema_s"]).abs() / (out["atr"] + 1e-12)
    out["pl_trigger"] = ((out["dir"] != 0) & (z <= p.delta)).astype(int)
    return out


def triple_barrier_labeling(df: pd.DataFrame, p: SignalParams,
                            col_trigger: str) -> pd.DataFrame:
    """对触发列（tl_long, tl_short, pl_trigger）产生的时点打标签: +1/-1/0"""
    out = df.copy()
    labels = np.zeros(len(out), dtype=int)
    idxs = np.where(out[col_trigger].values == 1)[0]
    closes = out["close"].values
    atrs = out["atr"].values

    for i in idxs:
        entry = closes[i]
        d = int(np.clip(p.max_hold_bars, 1, 10000))
        if out["dir"].iloc[i] >= 0:  # 多向
            tp = entry + p.tp_atr * atrs[i]
            sl = entry - p.sl_atr * atrs[i]
        else:  # 空向
            tp = entry - p.tp_atr * atrs[i]
            sl = entry + p.sl_atr * atrs[i]

        j_end = min(i + d, len(out) - 1)
        label = 0
        for j in range(i + 1, j_end + 1):
            high = out["high"].iloc[j]
            low = out["low"].iloc[j]
            if out["dir"].iloc[i] >= 0:
                hit_tp = high >= tp
                hit_sl = low <= sl
            else:
                hit_tp = low <= tp
                hit_sl = high >= sl
            if hit_tp and not hit_sl:
                label = 1
                break
            if hit_sl and not hit_tp:
                label = -1
                break
            # 若同根bar均命中，按先到价难以判定；保守记 0
        labels[i] = label

    out[f"label_{col_trigger}"] = labels
    return out


# =========================
# 特征工程（示例，可自行扩展）
# =========================

def build_features(df: pd.DataFrame) -> pd.DataFrame:
    X = pd.DataFrame(index=df.index)
    X["ma_diff"] = df["ema_s"] - df["ema_l"]
    X["ma_ratio"] = df["ema_s"] / (df["ema_l"] + 1e-12) - 1
    X["ma_slope_s"] = df["ema_s"].diff()
    X["ma_slope_l"] = df["ema_l"].diff()
    X["atr"] = df["atr"]
    X["bb_width"] = (df["close"].rolling(20).std() * 2).fillna(method="bfill")
    X["ret_1"] = df["close"].pct_change().fillna(0)
    X["ret_5"] = df["close"].pct_change(5).fillna(0)
    X["dir"] = df["dir"].fillna(0)
    # 时间特征
    X["hour"] = df.index.tz_convert("UTC").hour if df.index.tz is not None else df.index.hour
    X["dow"] = df.index.dayofweek
    # 归一化将在 Pipeline 中做
    X = X.replace([np.inf, -np.inf], np.nan).fillna(0)
    return X


# =========================
# 训练与评估
# =========================

def train_classifier(X: pd.DataFrame, y: pd.Series) -> Pipeline:
    # 仅预测正类(先触TP)概率，标签>0 为正类，其余为负
    y_bin = (y > 0).astype(int)
    clf = Pipeline([
        ("scaler", StandardScaler()),
        ("gbc", GradientBoostingClassifier(random_state=42))
    ])
    # 简单时间序列分割评估
    tscv = TimeSeriesSplit(n_splits=5)
    aucs = []
    for tr_idx, te_idx in tscv.split(X):
        clf.fit(X.iloc[tr_idx], y_bin.iloc[tr_idx])
        proba = clf.predict_proba(X.iloc[te_idx])[:, 1]
        try:
            aucs.append(roc_auc_score(y_bin.iloc[te_idx], proba))
        except Exception:
            pass
    if len(aucs) > 0:
        print(f"CV ROC-AUC mean={np.mean(aucs):.4f} +/- {np.std(aucs):.4f}")
    # 最终全量拟合
    clf.fit(X, y_bin)
    return clf


# =========================
# Backtrader 策略（使用 ML 概率阈值过滤）
# =========================

class MAMLPipeline(bt.Strategy if bt else object):
    params = dict(
        ma_s=24, ma_l=128, atr_n=14,
        tp_atr=3.0, sl_atr=1.5, max_hold_bars=24,
        delta=0.5,
        prob_threshold=0.65,
        model_path=None,
        risk_perc=0.003,  # 单笔风险占权益
        cash_start=10000,
    )

    def __init__(self):  # type: ignore
        if not bt:
            return
        self.data_close = self.datas[0].close
        self.data_high = self.datas[0].high
        self.data_low = self.datas[0].low
        self.ema_s = bt.ind.EMA(self.data_close, period=self.p.ma_s)
        self.ema_l = bt.ind.EMA(self.data_close, period=self.p.ma_l)
        # 近似 ATR
        self.atr = bt.ind.ATR(self.datas[0], period=self.p.atr_n)
        self.dir = bt.ind.Sign(self.ema_s - self.ema_l)
        # 交叉
        self.tl_long = bt.ind.CrossOver(self.ema_s, self.ema_l)  # +1 上穿, -1 下穿
        # ML 模型
        self.clf = None
        if self.p.model_path and os.path.exists(self.p.model_path):
            self.clf = joblib.load(self.p.model_path)

        self.bar_executed = None
        self.entry_price = None

    def _extract_features_bt(self) -> np.ndarray:
        # 与 build_features 保持一致（尽量）
        ma_s_val = float(self.ema_s[0])
        ma_l_val = float(self.ema_l[0])
        atr_val = float(self.atr[0]) + 1e-12
        close = float(self.data_close[0])
        # 构造与训练时对应的特征顺序
        feats = {
            "ma_diff": ma_s_val - ma_l_val,
            "ma_ratio": ma_s_val / (ma_l_val + 1e-12) - 1,
            "ma_slope_s": float(self.ema_s[0] - self.ema_s[-1]) if len(self.ema_s) > 1 else 0.0,
            "ma_slope_l": float(self.ema_l[0] - self.ema_l[-1]) if len(self.ema_l) > 1 else 0.0,
            "atr": atr_val,
            "bb_width": 2.0 * float(np.std([float(self.data_close[-i]) for i in range(1, min(21, len(self.data_close)))]) if len(self.data_close) > 20 else 0.0),
            "ret_1": float(self.data_close[0] / self.data_close[-1] - 1.0) if len(self.data_close) > 1 else 0.0,
            "ret_5": float(self.data_close[0] / self.data_close[-5] - 1.0) if len(self.data_close) > 5 else 0.0,
            "dir": float(1.0 if (ma_s_val - ma_l_val) > 0 else (-1.0 if (ma_s_val - ma_l_val) < 0 else 0.0)),
            "hour": 0.0,  # BT bar 无直接小时信息；如需要请在数据馈送时注入
            "dow": 0.0,
        }
        arr = np.array([feats[k] for k in [
            "ma_diff","ma_ratio","ma_slope_s","ma_slope_l","atr","bb_width","ret_1","ret_5","dir","hour","dow"
        ]], dtype=float).reshape(1, -1)
        return arr

    def next(self):  # type: ignore
        if not bt:
            return
        # 计算触发
        price = self.data_close[0]
        atr_val = float(self.atr[0]) + 1e-12
        direction = 1 if (self.ema_s[0] - self.ema_l[0]) > 0 else -1

        # 趋势腿：上/下穿
        tl_signal = 1 if self.tl_long[0] > 0 else (-1 if self.tl_long[0] < 0 else 0)
        # 回踩腿：|p-ema_s|/ATR <= delta
        pl_trigger = 1 if (abs(price - self.ema_s[0]) / atr_val <= self.p.delta and direction != 0) else 0

        # 合并信号（示例：优先 TL，否则 PL）
        raw_signal = 0
        if tl_signal != 0:
            raw_signal = tl_signal
        elif pl_trigger:
            raw_signal = direction

        if raw_signal == 0:
            return

        # ML 过滤
        if self.clf is not None:
            X = self._extract_features_bt()
            p = float(self.clf.predict_proba(X)[:, 1])
            if p < self.p.prob_threshold:
                return  # 不做这笔

        # 风控：已有持仓就不加仓（简单版）
        if self.position.size != 0:
            return

        # 头寸 sizing：风险单位法（大致）
        cash = self.broker.get_cash()
        risk_perc = self.p.risk_perc
        sl_dist = self.p.sl_atr * atr_val
        if sl_dist <= 0:
            return
        risk_amt = cash * risk_perc
        size = max(1, int(risk_amt / sl_dist))

        if raw_signal > 0:
            self.buy(size=size)
            self.entry_price = price
        elif raw_signal < 0:
            self.sell(size=size)
            self.entry_price = price
        self.bar_executed = len(self)

        # 退出逻辑：TP/SL/时间止盈（使用 next 中检查）

        # 检查止盈止损
        if self.position.size != 0 and self.entry_price is not None:
            dir_pos = 1 if self.position.size > 0 else -1
            tp = self.entry_price + dir_pos * self.p.tp_atr * atr_val
            sl = self.entry_price - dir_pos * self.p.sl_atr * atr_val
            # 简化：若当前价穿越 TP/SL 就平
            if (dir_pos > 0 and price >= tp) or (dir_pos < 0 and price <= tp):
                self.close()
                self.entry_price = None
            elif (dir_pos > 0 and price <= sl) or (dir_pos < 0 and price >= sl):
                self.close()
                self.entry_price = None
            # 时间止盈
            elif self.bar_executed is not None and (len(self) - self.bar_executed) >= self.p.max_hold_bars:
                self.close()
                self.entry_price = None


# =========================
# 回测 + Optuna 优化
# =========================

def run_backtest(df: pd.DataFrame, params: Dict[str, Any]) -> Dict[str, float]:
    if not bt:
        raise RuntimeError("需要安装 backtrader 才能回测")
    cerebro = bt.Cerebro(stdstats=False)
    data = bt.feeds.PandasData(dataname=df)
    cerebro.adddata(data)
    cerebro.broker.setcash(params.get("cash_start", 10000.0))
    cerebro.broker.setcommission(commission=params.get("commission", 0.0005))
    cerebro.addstrategy(MAMLPipeline, **params)
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
    cerebro.addanalyzer(bt.analyzers.SharpeRatio_A, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='dd')
    result = cerebro.run(maxcpus=1)
    strat = result[0]
    port_value = cerebro.broker.getvalue()
    # 取指标
    sharpe = strat.analyzers.sharpe.get_analysis().get('sharperatio', np.nan)
    dd = strat.analyzers.dd.get_analysis().get('max', {}).get('drawdown', np.nan)
    # 回测收益率
    start_cash = params.get("cash_start", 10000.0)
    ret = (port_value - start_cash) / start_cash
    out = {
        "ret": float(ret),
        "sharpe": float(sharpe) if sharpe is not None else np.nan,
        "maxdd": float(dd) if not (dd is None) else np.nan,
        "final_value": float(port_value),
    }
    return out


def optuna_objective(trial: "optuna.trial.Trial", base_df: pd.DataFrame, model_path: Optional[str]) -> float:
    # 建立参数空间
    ma_s = trial.suggest_int("ma_s", 12, 48)
    ma_l = trial.suggest_int("ma_l", 64, 256)
    if ma_l - ma_s < 8:
        ma_l = ma_s + 8
    tp_atr = trial.suggest_float("tp_atr", 1.5, 4.5)
    sl_atr = trial.suggest_float("sl_atr", 0.8, 2.5)
    delta = trial.suggest_float("delta", 0.25, 0.7)
    prob_th = trial.suggest_float("prob_threshold", 0.5, 0.9)

    params = dict(ma_s=ma_s, ma_l=ma_l, atr_n=14,
                  tp_atr=tp_atr, sl_atr=sl_atr, max_hold_bars=24,
                  delta=delta, prob_threshold=prob_th,
                  model_path=model_path,
                  commission=0.0007, cash_start=10000)
    metrics = run_backtest(base_df, params)
    score = (metrics.get("sharpe") or 0.0) - 0.002 * (metrics.get("maxdd") or 0.0)
    # 可加入更多稳定性罚项
    return score


# =========================
# ccxt 实盘骨架（谨慎）
# =========================

class LiveTrader:
    def __init__(self, exchange: str, api_key: str, api_secret: str, symbol: str, timeframe: str,
                 model_path: str, prob_threshold: float,
                 ma_s=24, ma_l=128, atr_n=14, tp_atr=3.0, sl_atr=1.5, delta=0.5):
        if not ccxt:
            raise RuntimeError("需要安装 ccxt 才能实盘")
        ex_class = getattr(ccxt, exchange)
        self.ex = ex_class({"apiKey": api_key, "secret": api_secret, "enableRateLimit": True})
        self.symbol = symbol
        self.timeframe = timeframe
        self.clf = joblib.load(model_path)
        self.tau = prob_threshold
        self.p = SignalParams(ma_s=ma_s, ma_l=ma_l, atr_n=atr_n, tp_atr=tp_atr, sl_atr=sl_atr, delta=delta)
        self.position_side = 0  # -1/0/1 简化示例

    def fetch_ohlcv_df(self, limit=300) -> pd.DataFrame:
        ohlcv = self.ex.fetch_ohlcv(self.symbol, timeframe=self.timeframe, limit=limit)
        df = pd.DataFrame(ohlcv, columns=["timestamp","open","high","low","close","volume"])\
               .assign(datetime=lambda x: pd.to_datetime(x.timestamp, unit="ms", utc=True))\
               .set_index("datetime")[['open','high','low','close','volume']]
        return df.astype(float)

    def step(self):
        df = self.fetch_ohlcv_df(limit=400)
        feats_df = build_signals(df, self.p)
        X = build_features(feats_df)
        # 最近一根作为判定
        proba = float(self.clf.predict_proba(X.tail(1))[:, 1])
        last = feats_df.iloc[-1]
        direction = 1 if last["ema_s"] - last["ema_l"] > 0 else -1
        z = abs(last["close"] - last["ema_s"]) / (last["atr"] + 1e-12)
        tl_long = int((feats_df["ema_s"].iloc[-1] > feats_df["ema_l"].iloc[-1]) and (feats_df["ema_s"].iloc[-2] <= feats_df["ema_l"].iloc[-2]))
        tl_short = int((feats_df["ema_s"].iloc[-1] < feats_df["ema_l"].iloc[-1]) and (feats_df["ema_s"].iloc[-2] >= feats_df["ema_l"].iloc[-2]))
        pl_trigger = int((direction != 0) and (z <= self.p.delta))
        raw_signal = 0
        if tl_long:
            raw_signal = 1
        elif tl_short:
            raw_signal = -1
        elif pl_trigger:
            raw_signal = direction

        if raw_signal != 0 and proba >= self.tau:
            # 下单示例（市价，注意使用期货/永续需设置 margin 模式/方向等）
            side = 'buy' if raw_signal > 0 else 'sell'
            qty = 0.001  # TODO: 根据风险单位计算
            try:
                order = self.ex.create_market_order(self.symbol, side, qty)
                print("Placed order:", order)
                self.position_side = raw_signal
            except Exception as e:
                print("Place order error:", e)
        else:
            print(f"No trade. proba={proba:.3f} raw_signal={raw_signal}")


# =========================
# 主流程：训练/回测/实盘
# =========================

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--mode', required=True, choices=['train','backtest','live'])
    parser.add_argument('--csv')
    parser.add_argument('--symbol', default='BTCUSDT')
    parser.add_argument('--timeframe', default='5m')
    parser.add_argument('--study_trials', type=int, default=50)
    parser.add_argument('--oos_months', type=int, default=1)
    parser.add_argument('--model_path')
    parser.add_argument('--prob_threshold', type=float, default=0.65)
    # 策略参数（可被 optuna 覆盖）
    parser.add_argument('--ma_s', type=int, default=24)
    parser.add_argument('--ma_l', type=int, default=128)
    parser.add_argument('--tp_atr', type=float, default=3.0)
    parser.add_argument('--sl_atr', type=float, default=1.5)
    parser.add_argument('--delta', type=float, default=0.5)
    # ccxt 实盘配置
    parser.add_argument('--exchange', default='binanceusdm')
    parser.add_argument('--api_key')
    parser.add_argument('--api_secret')

    args = parser.parse_args()

    if args.mode in ('train','backtest'):
        assert args.csv, '--csv 不能为空'
        df = read_ohlcv_csv(args.csv)
        # 生成基础指标与信号
        p = SignalParams(ma_s=args.ma_s, ma_l=args.ma_l, tp_atr=args.tp_atr, sl_atr=args.sl_atr, delta=args.delta)
        feats_df = build_signals(df, p)

    if args.mode == 'train':
        # 打标（TL + PL 合并做一个训练集，或分别训练两个模型 — 这里示例合并）
        tl_long_lab = triple_barrier_labeling(feats_df, p, 'tl_long')
        tl_short_lab = triple_barrier_labeling(feats_df, p, 'tl_short')
        pl_lab = triple_barrier_labeling(feats_df, p, 'pl_trigger')
        lab_df = feats_df.copy()
        # 合并标签：优先 TL，其次 PL（示例逻辑，可自行调整）
        lab_df['label'] = 0
        for col in [f'label_tl_long', f'label_tl_short', f'label_pl_trigger']:
            if col in tl_long_lab.columns:
                lab_df[col] = tl_long_lab.get(col, 0)
            if col in tl_short_lab.columns:
                lab_df[col] = tl_short_lab.get(col, 0)
            if col in pl_lab.columns:
                lab_df[col] = pl_lab.get(col, 0)
        # 简单合并: 有 +1 则 +1；有 -1 则 -1；否则 0
        lab_df['label'] = 0
        for i in range(len(lab_df)):
            vals = [lab_df.get(f'label_{c}', pd.Series([0]*len(lab_df))).iloc[i] for c in ['tl_long','tl_short','pl_trigger']]
            if 1 in vals:
                lab_df.iloc[i, lab_df.columns.get_loc('label')] = 1
            elif -1 in vals:
                lab_df.iloc[i, lab_df.columns.get_loc('label')] = -1
            else:
                lab_df.iloc[i, lab_df.columns.get_loc('label')] = 0

        X = build_features(lab_df)
        y = lab_df['label']
        # 过滤极早期缺失
        mask = X.index >= X.index[100] if len(X) > 100 else np.ones(len(X), dtype=bool)
        X, y = X[mask], y[mask]

        clf = train_classifier(X, y)
        os.makedirs('models', exist_ok=True)
        model_path = f"models/{args.symbol}_{args.timeframe}.pkl"
        joblib.dump(clf, model_path)
        print(f"模型已保存: {model_path}")

        # 用 Optuna 搜索阈值与参数（载入保存的模型）
        if not optuna:
            print("未安装 optuna，跳过超参搜索。")
            return
        study = optuna.create_study(direction='maximize')
        # 为回测准备数据（Backtrader 用）
        # 回测数据需是数值型，并且 index 为 datetime
        bt_df = df.copy()
        # 简单：将 close/hlcv 保留；Backtrader 内部指标会再计算
        def objective(trial):
            return optuna_objective(trial, bt_df, model_path)
        study.optimize(objective, n_trials=args.study_trials)
        print("最佳 Trial:", study.best_trial.number)
        print("最佳值:", study.best_value)
        print("最佳参数:", json.dumps(study.best_trial.params, ensure_ascii=False, indent=2))

    elif args.mode == 'backtest':
        # 使用指定模型 + 参数进行回测
        assert args.model_path and os.path.exists(args.model_path), '--model_path 不存在'
        params = dict(
            ma_s=args.ma_s, ma_l=args.ma_l, atr_n=14,
            tp_atr=args.tp_atr, sl_atr=args.sl_atr, max_hold_bars=24,
            delta=args.delta, prob_threshold=args.prob_threshold,
            model_path=args.model_path,
            commission=0.0007, cash_start=10000,
        )
        metrics = run_backtest(df, params)
        print("回测指标:", json.dumps(metrics, ensure_ascii=False, indent=2))

    elif args.mode == 'live':
        # **慎用** 实盘骨架
        assert args.api_key and args.api_secret, '需要提供 --api_key / --api_secret'
        assert args.model_path and os.path.exists(args.model_path), '--model_path 不存在'
        trader = LiveTrader(exchange=args.exchange, api_key=args.api_key, api_secret=args.api_secret,
                            symbol=args.symbol.replace('USDT','/USDT') if '/' not in args.symbol else args.symbol,
                            timeframe=args.timeframe, model_path=args.model_path,
                            prob_threshold=args.prob_threshold,
                            ma_s=args.ma_s, ma_l=args.ma_l, tp_atr=args.tp_atr, sl_atr=args.sl_atr, delta=args.delta)
        print("启动实盘骨架，Ctrl+C 退出...")
        try:
            while True:
                trader.step()
                time.sleep(1)
        except KeyboardInterrupt:
            print('已停止。')


if __name__ == '__main__':
    main()
