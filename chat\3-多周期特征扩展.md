# 目标
把这个 **FeatureBuilder 类** 进一步扩展一下，加上 **自动选择主周期**（比如 1min、5min、15min 都能用），而不是写死在 5m

现在帮你把之前的\*\*“短周期均线跟踪 + 长周期均线反弹/反转”**的特征模板升级成一个**多层次、可扩展的特征体系\*\*，方便后续接入机器学习模型。这个模板会兼顾 **趋势、波动、动量、结构、跨周期确认**五个维度。

---

# 📑 扩展特征模板（适配短周期均线跟踪系统）

## 1. 趋势类（Trend Features）

> 描述价格在不同时间尺度上的方向与强弱

* `ma_short`, `ma_long`: 短/长均线
* `ma_diff = (ma_short - ma_long) / ma_long`
* `ema_ratio = ema(20)/ema(50)`
* `trend_slope = slope(ma_long, lookback=20)`
* `trend_persistence = % of last 30 bars above/below ma_long`

## 2. 波动类（Volatility Features）

> 描述价格波动和不确定性，帮助判断反转与假突破

* `atr = ATR(14)`
* `bollinger_width = (upper - lower) / ma`
* `hv_20 = realized_volatility(20)`
* `vol_of_vol = rolling_std(ATR, 20)`
* `z_score_price = (price - ma_long)/std(lookback=50)`

## 3. 动量类（Momentum Features）

> 捕捉“动能衰竭”或“趋势延续”的特征

* `rsi_14`, `stoch_14`, `macd`
* `momentum = price - price.shift(10)`
* `rate_of_change = (price/price.shift(10))-1`
* `candle_body_strength = mean((close-open)/(high-low), 5)`

## 4. 结构类（Market Structure Features）

> 识别关键点位与价量结构

* `support_resistance_gap = |price - nearest_SR|/price`
* `recent_high_low_ratio = (close - rolling_min(20)) / (rolling_max(20)-rolling_min(20))`
* `volume_surge = volume / rolling_mean(volume, 20)`
* `order_imbalance (if L2 data available)`
* `false_breakout_flag = (high > prev_high) & (close < prev_high)`

## 5. 跨周期确认（Multi-Timeframe Features）

> 短周期策略常见问题是被长周期结构打脸，跨周期特征能避免

* `long_tf_trend = slope(ma_long_tf, 50)`
* `alignment = sign(ma_short_tf - ma_long_tf)`
* `cross_tf_confirmation = 1 if (5m_trend==15m_trend) else 0`
* `multi_tf_volatility_ratio = ATR(5m)/ATR(1h)`
* `macro_filter = BTC.dominance, ETHBTC ratio, funding_rate`

---

# 🎯 使用方式

1. **信号识别**：

   * 当短周期均线穿越长周期均线时，额外检查 `trend_slope`, `bollinger_width`, `cross_tf_confirmation`，以区分趋势延续 vs 假突破。

2. **模型训练**：

   * 将这些特征拼接成输入向量 `X`，标签 `y` 定义为“后 N 根K线收益是否大于阈值”。
   * 模型（RandomForest, XGBoost, LSTM）将学到**哪些交叉值得做，哪些需要回避**。

3. **实盘部署**：

   * 每个特征在实盘中都可实时计算，作为过滤器。
   * 比如：当短周期金叉，但长周期趋势斜率为负 & 波动过大（ATR 高位），则拒绝开仓。

---

👉 这个模板已经够支撑一个可训练的 ML 模型了，但还可以扩展成：

* **事件驱动特征**（链上指标、资金费率、新闻情绪）
* **结构化标签**（区分趋势行情 vs 震荡行情，然后模型分流）
* **元特征**（基于前一轮模型输出的信心度再加工）

