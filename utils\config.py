"""
配置管理模块
统一管理项目配置参数
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any
import os
import json


@dataclass
class SignalParams:
    """信号参数配置"""
    ma_s: int = 24
    ma_l: int = 128
    atr_n: int = 14
    tp_atr: float = 3.0
    sl_atr: float = 1.5
    max_hold_bars: int = 24
    delta: float = 0.5  # 回踩触发阈值: |price - ema_s|/ATR <= delta


@dataclass
class ModelConfig:
    """模型配置"""
    model_type: str = "GradientBoostingClassifier"
    random_state: int = 42
    n_splits: int = 5  # 时间序列交叉验证分割数
    prob_threshold: float = 0.65


@dataclass
class BacktestConfig:
    """回测配置"""
    cash_start: float = 10000.0
    commission: float = 0.0007
    risk_perc: float = 0.003  # 单笔风险占权益


@dataclass
class OptimizationConfig:
    """优化配置"""
    n_trials: int = 50
    direction: str = "maximize"
    # 参数搜索范围
    ma_s_range: tuple = (12, 48)
    ma_l_range: tuple = (64, 256)
    tp_atr_range: tuple = (1.5, 4.5)
    sl_atr_range: tuple = (0.8, 2.5)
    delta_range: tuple = (0.25, 0.7)
    prob_threshold_range: tuple = (0.5, 0.9)


@dataclass
class LiveTradingConfig:
    """实盘交易配置"""
    exchange: str = "binanceusdm"
    symbol: str = "BTC/USDT"
    timeframe: str = "5m"
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    position_size: float = 0.001  # 默认仓位大小
    max_positions: int = 1  # 最大持仓数量


class Config:
    """主配置类"""

    def __init__(self, config_file: Optional[str] = None):
        self.signal_params = SignalParams()
        self.model_config = ModelConfig()
        self.backtest_config = BacktestConfig()
        self.optimization_config = OptimizationConfig()
        self.live_trading_config = LiveTradingConfig()

        if config_file and os.path.exists(config_file):
            self.load_from_file(config_file)

    def load_from_file(self, config_file: str):
        """从JSON文件加载配置"""
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)

        # 更新各个配置对象
        if 'signal_params' in config_data:
            for key, value in config_data['signal_params'].items():
                if hasattr(self.signal_params, key):
                    setattr(self.signal_params, key, value)

        if 'model_config' in config_data:
            for key, value in config_data['model_config'].items():
                if hasattr(self.model_config, key):
                    setattr(self.model_config, key, value)

        if 'backtest_config' in config_data:
            for key, value in config_data['backtest_config'].items():
                if hasattr(self.backtest_config, key):
                    setattr(self.backtest_config, key, value)

        if 'optimization_config' in config_data:
            for key, value in config_data['optimization_config'].items():
                if hasattr(self.optimization_config, key):
                    setattr(self.optimization_config, key, value)

        if 'live_trading_config' in config_data:
            for key, value in config_data['live_trading_config'].items():
                if hasattr(self.live_trading_config, key):
                    setattr(self.live_trading_config, key, value)

    def save_to_file(self, config_file: str):
        """保存配置到JSON文件"""
        config_data = {
            'signal_params': self.signal_params.__dict__,
            'model_config': self.model_config.__dict__,
            'backtest_config': self.backtest_config.__dict__,
            'optimization_config': self.optimization_config.__dict__,
            'live_trading_config': self.live_trading_config.__dict__
        }

        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)

    def update_from_args(self, args):
        """从命令行参数更新配置"""
        # 更新信号参数
        if hasattr(args, 'ma_s') and args.ma_s:
            self.signal_params.ma_s = args.ma_s
        if hasattr(args, 'ma_l') and args.ma_l:
            self.signal_params.ma_l = args.ma_l
        if hasattr(args, 'tp_atr') and args.tp_atr:
            self.signal_params.tp_atr = args.tp_atr
        if hasattr(args, 'sl_atr') and args.sl_atr:
            self.signal_params.sl_atr = args.sl_atr
        if hasattr(args, 'delta') and args.delta:
            self.signal_params.delta = args.delta

        # 更新模型配置
        if hasattr(args, 'prob_threshold') and args.prob_threshold:
            self.model_config.prob_threshold = args.prob_threshold

        # 更新实盘配置
        if hasattr(args, 'exchange') and args.exchange:
            self.live_trading_config.exchange = args.exchange
        if hasattr(args, 'symbol') and args.symbol:
            self.live_trading_config.symbol = args.symbol
        if hasattr(args, 'timeframe') and args.timeframe:
            self.live_trading_config.timeframe = args.timeframe
        if hasattr(args, 'api_key') and args.api_key:
            self.live_trading_config.api_key = args.api_key
        if hasattr(args, 'api_secret') and args.api_secret:
            self.live_trading_config.api_secret = args.api_secret
