"""
特征一致性测试
验证训练时和回测时的特征提取结果完全一致
"""

import sys
import os
import numpy as np
import pandas as pd
from typing import Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from features.feature_extractor import FeatureExtractor
from features.indicators import add_all_indicators
from config.feature_config import FeatureConfigTemplates
from utils.config import SignalParams
from utils.logger import get_logger

logger = get_logger("test_features")


class MockBacktraderStrategy:
    """
    模拟Backtrader策略对象，用于测试
    """
    
    def __init__(self, df: pd.DataFrame):
        self.df = df
        self.current_index = 50  # 从第50行开始，确保有足够的历史数据
        
        # 模拟Backtrader的数据访问方式
        self.data_close = MockIndicator(df['close'], self.current_index)
        self.ema_s = MockIndicator(df['ema_s'], self.current_index)
        self.ema_l = MockIndicator(df['ema_l'], self.current_index)
        self.atr = MockIndicator(df['atr'], self.current_index)


class MockIndicator:
    """
    模拟Backtrader指标对象
    """
    
    def __init__(self, series: pd.Series, current_index: int):
        self.series = series
        self.current_index = current_index
    
    def __getitem__(self, index):
        """模拟Backtrader的索引访问方式"""
        if index == 0:
            return self.series.iloc[self.current_index]
        elif index < 0:
            return self.series.iloc[self.current_index + index]
        else:
            raise IndexError("Positive indices not supported in this mock")
    
    def __len__(self):
        return self.current_index + 1


def create_test_data(n_samples: int = 1000) -> pd.DataFrame:
    """
    创建测试数据
    
    Args:
        n_samples: 样本数量
        
    Returns:
        测试数据DataFrame
    """
    np.random.seed(42)
    
    # 生成基础OHLCV数据
    dates = pd.date_range('2023-01-01', periods=n_samples, freq='1H')
    
    # 生成价格数据（随机游走）
    returns = np.random.normal(0, 0.01, n_samples)
    prices = 100 * np.exp(np.cumsum(returns))
    
    df = pd.DataFrame({
        'open': prices * (1 + np.random.normal(0, 0.001, n_samples)),
        'high': prices * (1 + np.abs(np.random.normal(0, 0.005, n_samples))),
        'low': prices * (1 - np.abs(np.random.normal(0, 0.005, n_samples))),
        'close': prices,
        'volume': np.random.lognormal(10, 1, n_samples),
    }, index=dates)
    
    # 确保OHLC关系正确
    df['high'] = np.maximum(df['high'], np.maximum(df['open'], df['close']))
    df['low'] = np.minimum(df['low'], np.minimum(df['open'], df['close']))
    
    return df


def test_feature_consistency(config_name: str = "standard"):
    """
    测试特征一致性
    
    Args:
        config_name: 配置名称
    """
    logger.info(f"开始测试特征一致性 - 配置: {config_name}")
    
    # 创建测试数据
    df = create_test_data(1000)
    
    # 获取特征配置
    if config_name == "minimal":
        feature_config = FeatureConfigTemplates.minimal()
    elif config_name == "enhanced":
        feature_config = FeatureConfigTemplates.enhanced()
    elif config_name == "comprehensive":
        feature_config = FeatureConfigTemplates.comprehensive()
    else:
        feature_config = FeatureConfigTemplates.standard()
    
    # 添加技术指标
    signal_params = SignalParams()
    df_with_indicators = add_all_indicators(df, signal_params)
    
    # 创建特征提取器
    extractor = FeatureExtractor(signal_params, feature_config)
    
    # 方法1：从DataFrame提取特征（训练时的方式）
    features_df = extractor.extract_features_from_dataframe(df_with_indicators)
    
    # 方法2：从模拟Backtrader策略提取特征（回测时的方式）
    mock_strategy = MockBacktraderStrategy(df_with_indicators)
    features_bt = extractor.extract_features_from_backtrader(mock_strategy)
    
    # 获取对应时间点的DataFrame特征
    test_index = mock_strategy.current_index
    features_df_row = features_df.iloc[test_index].values
    features_bt_row = features_bt.flatten()
    
    # 比较特征数量
    logger.info(f"DataFrame特征数量: {len(features_df_row)}")
    logger.info(f"Backtrader特征数量: {len(features_bt_row)}")
    logger.info(f"期望特征数量: {extractor.get_feature_count()}")
    
    # 检查特征数量是否一致
    if len(features_df_row) != len(features_bt_row):
        logger.error(f"特征数量不一致! DataFrame: {len(features_df_row)}, Backtrader: {len(features_bt_row)}")
        return False
    
    if len(features_df_row) != extractor.get_feature_count():
        logger.error(f"特征数量与期望不符! 实际: {len(features_df_row)}, 期望: {extractor.get_feature_count()}")
        return False
    
    # 比较特征值
    max_diff = np.max(np.abs(features_df_row - features_bt_row))
    mean_diff = np.mean(np.abs(features_df_row - features_bt_row))
    
    logger.info(f"特征值最大差异: {max_diff:.6f}")
    logger.info(f"特征值平均差异: {mean_diff:.6f}")
    
    # 检查特征值是否一致（允许小的数值误差）
    tolerance = 1e-6
    if max_diff > tolerance:
        logger.error(f"特征值差异过大! 最大差异: {max_diff:.6f} > 容忍度: {tolerance}")
        
        # 显示差异较大的特征
        feature_names = extractor.get_feature_names()
        diffs = np.abs(features_df_row - features_bt_row)
        large_diff_indices = np.where(diffs > tolerance)[0]
        
        for idx in large_diff_indices:
            if idx < len(feature_names):
                logger.error(f"  {feature_names[idx]}: DataFrame={features_df_row[idx]:.6f}, "
                           f"Backtrader={features_bt_row[idx]:.6f}, 差异={diffs[idx]:.6f}")
        
        return False
    
    logger.info("✅ 特征一致性测试通过!")
    return True


def test_all_configurations():
    """测试所有配置的特征一致性"""
    logger.info("开始测试所有配置的特征一致性")
    
    configs = ["minimal", "standard", "enhanced"]
    results = {}
    
    for config_name in configs:
        try:
            result = test_feature_consistency(config_name)
            results[config_name] = result
            logger.info(f"{config_name} 配置测试结果: {'通过' if result else '失败'}")
        except Exception as e:
            logger.error(f"{config_name} 配置测试失败: {e}")
            results[config_name] = False
    
    # 汇总结果
    logger.info("\n=== 测试结果汇总 ===")
    all_passed = True
    for config_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{config_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        logger.info("🎉 所有配置的特征一致性测试都通过了!")
    else:
        logger.error("⚠️ 部分配置的特征一致性测试失败!")
    
    return all_passed


if __name__ == "__main__":
    # 运行测试
    test_all_configurations()
