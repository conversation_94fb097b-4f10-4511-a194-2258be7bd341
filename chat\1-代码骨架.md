之前那个单文件脚手架实现了整个 pipeline 的核心功能：数据读写、指标、规则信号、三重边界标注、特征、sklearn训练、Backtrader策略、Optuna 搜索、ccxt 实盘骨架。

但它是单体（monolith），没有“关注点分离”：生产化、测试、复用、并行开发与 CI 都会变得困难。我们需要把它拆成模块化目录。

# 项目结构化后的代码骨架

以下是将之前的**单体脚手架**按照“关注点分离 (Separation of Concerns)”理念拆分后的模块化结构代码骨架。每个文件夹内的代码都能独立运行，其输出成为下游模块的输入。

```bash
quant_project/
│
├── data/                  # 数据下载与管理
│   ├── download.py        # 用 ccxt 下载历史K线
│   ├── preprocess.py      # 清洗/补齐/合并数据
│   └── split.py           # 训练/验证/测试集切分
│
├── features/              # 特征工程
│   ├── indicators.py      # 均线、ATR、布林等技术指标
│   ├── labeling.py        # 事件打标 (三重障碍, 回踩标注)
│   └── build_features.py  # 合并生成训练用特征表
│
├── models/                # ML 训练与评估
│   ├── train.py           # 训练 sklearn 模型（逻辑回归、RF等）
│   ├── evaluate.py        # 模型验证与指标输出
│   └── save_load.py       # 模型持久化与加载
│
├── strategy/              # Backtrader 策略
│   ├── base_strategy.py   # 双均线+回踩的基础策略
│   ├── ml_filter.py       # 策略中嵌入 ML 过滤器
│   └── run_backtest.py    # Backtrader 回测主入口
│
├── optimization/          # 超参优化
│   ├── optuna_objective.py # 定义优化目标函数
│   ├── run_optuna.py      # 启动 Optuna 搜索
│   └── visualize.py       # 结果可视化
│
├── live/                  # 实盘交易
│   ├── broker.py          # ccxt 下单封装
│   ├── live_trade.py      # 实盘交易循环
│   └── risk.py            # 风控逻辑 (止损/限仓)
│
└── utils/                 # 工具类
    ├── logger.py          # 日志系统
    ├── config.py          # 配置文件管理
    └── helpers.py         # 通用工具函数
```

---

## 各模块的**小任务拆解**：

### `data/`
- **download.py**: 从 ccxt 下载多品种K线，存成 parquet/csv。
- **preprocess.py**: 填补缺失值，统一时区，去掉异常点。
- **split.py**: 按时间切分为训练/验证/测试集。

### `features/`
- **indicators.py**: 双均线、ATR、布林、RSI等。
- **labeling.py**: 实现“三重障碍法”打标签，生成 y。
- **build_features.py**: 合并指标 + 标签，输出 ML 可用表格。

### `models/`
- **train.py**: 读取特征表，训练 sklearn 模型。
- **evaluate.py**: 输出混淆矩阵、AUC、F1、收益曲线。
- **save_load.py**: joblib/pickle 保存和加载模型。

### `strategy/`
- **base_strategy.py**: 双均线+回踩信号生成。
- **ml_filter.py**: 引入 ML 概率阈值过滤。
- **run_backtest.py**: 批量跑回测，保存结果。

### `optimization/`
- **optuna_objective.py**: 定义搜索目标（如夏普比率最大化）。
- **run_optuna.py**: 启动搜索，管理 Trials。
- **visualize.py**: 输出参数敏感性图、最佳参数对比。

### `live/`
- **broker.py**: 封装 ccxt 的下单、撤单、查询功能。
- **live_trade.py**: 实盘循环：拉行情 → 算信号 → ML过滤 → 下单。
- **risk.py**: 风险控制：单笔止损、资金管理、熔断开关。

### `utils/`
- **logger.py**: 输出回测日志、实盘日志。
- **config.py**: 统一配置文件（交易对、参数、模型路径）。
- **helpers.py**: 通用函数（如时间戳转换、滑点模拟）。

---

✅ 这样，就可以把之前的**单体脚手架**逐步填充到这些模块里。
现在每个部分都被解耦：data 负责数据，features 负责特征，models 管 ML，strategy 管策略，optimization 做寻优，live 做实盘，utils 管工具。每个模块能独立使用（比如单独跑特征生成，或单独测试一个策略），也方便多人协作。
