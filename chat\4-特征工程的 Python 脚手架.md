帮你写一个**特征工程脚手架**，基于 `pandas + ta`（技术指标库）来快速生成你之前要的五大类特征。这样你可以直接在 K 线数据（OHLCV）上批量生成训练用的特征矩阵。

我会写成一个模块化结构：

* `features/feature_generator.py`：核心特征生成器
* 支持 **趋势、波动、动量、结构、跨周期确认**
* 你只要传入一个 DataFrame（带 `open, high, low, close, volume`），就能得到特征矩阵

---

## 📂 features/feature\_generator.py

```python
import pandas as pd
import numpy as np
import ta  # pip install ta


class FeatureGenerator:
    def __init__(self, df: pd.DataFrame, freq="5min"):
        """
        df: 必须包含 [open, high, low, close, volume] 列
        freq: 当前K线周期 (用于跨周期特征拼接)
        """
        self.df = df.copy()
        self.freq = freq

    def add_trend_features(self):
        self.df["ma_short"] = self.df["close"].rolling(10).mean()
        self.df["ma_long"] = self.df["close"].rolling(50).mean()
        self.df["ma_diff"] = (self.df["ma_short"] - self.df["ma_long"]) / self.df["ma_long"]
        self.df["ema_ratio"] = ta.trend.EMAIndicator(self.df["close"], 20).ema_indicator() / \
                               ta.trend.EMAIndicator(self.df["close"], 50).ema_indicator()
        # 长周期趋势斜率
        self.df["trend_slope"] = self.df["ma_long"].diff(5)
        return self

    def add_volatility_features(self):
        self.df["atr"] = ta.volatility.AverageTrueRange(self.df["high"], self.df["low"], self.df["close"], 14).average_true_range()
        bb = ta.volatility.BollingerBands(self.df["close"], 20, 2)
        self.df["bollinger_width"] = (bb.bollinger_hband() - bb.bollinger_lband()) / self.df["close"].rolling(20).mean()
        self.df["z_score_price"] = (self.df["close"] - self.df["ma_long"]) / self.df["close"].rolling(50).std()
        return self

    def add_momentum_features(self):
        self.df["rsi_14"] = ta.momentum.RSIIndicator(self.df["close"], 14).rsi()
        self.df["stoch_14"] = ta.momentum.StochasticOscillator(self.df["high"], self.df["low"], self.df["close"], 14).stoch()
        macd = ta.trend.MACD(self.df["close"])
        self.df["macd"] = macd.macd()
        self.df["momentum_10"] = self.df["close"] - self.df["close"].shift(10)
        self.df["roc_10"] = self.df["close"].pct_change(10)
        return self

    def add_structure_features(self):
        # 20日高低点位置
        self.df["recent_high"] = self.df["close"].rolling(20).max()
        self.df["recent_low"] = self.df["close"].rolling(20).min()
        self.df["recent_high_low_ratio"] = (self.df["close"] - self.df["recent_low"]) / (self.df["recent_high"] - self.df["recent_low"] + 1e-9)
        # 成交量异常
        self.df["volume_surge"] = self.df["volume"] / (self.df["volume"].rolling(20).mean() + 1e-9)
        # 假突破标记
        self.df["false_breakout"] = ((self.df["high"] > self.df["high"].shift(1)) & (self.df["close"] < self.df["high"].shift(1))).astype(int)
        return self

    def add_multi_timeframe_features(self, df_higher: pd.DataFrame = None):
        """
        df_higher: 高周期的OHLCV DataFrame, index 要和低周期对齐（可用 resample+ffill）
        """
        if df_higher is not None:
            df_higher = df_higher.copy()
            df_higher["ma_long_tf"] = df_higher["close"].rolling(50).mean()
            df_higher["trend_slope_tf"] = df_higher["ma_long_tf"].diff(5)
            # 拼接回低周期
            self.df = self.df.join(df_higher[["ma_long_tf", "trend_slope_tf"]], how="left")
            self.df["cross_tf_confirmation"] = np.sign(self.df["ma_long"] - self.df["ma_long_tf"])
        return self

    def generate_all(self, df_higher=None):
        return (
            self.add_trend_features()
                .add_volatility_features()
                .add_momentum_features()
                .add_structure_features()
                .add_multi_timeframe_features(df_higher)
                .df
        )


# 示例用法
if __name__ == "__main__":
    # 构造示例数据
    dates = pd.date_range("2022-01-01", periods=200, freq="5min")
    df = pd.DataFrame({
        "open": np.random.rand(200) * 100,
        "high": np.random.rand(200) * 100,
        "low": np.random.rand(200) * 100,
        "close": np.random.rand(200) * 100,
        "volume": np.random.randint(100, 1000, 200)
    }, index=dates)

    fg = FeatureGenerator(df)
    features = fg.generate_all()
    print(features.tail())
```

---

## 🚀 用法流程

1. 准备 OHLCV DataFrame（比如用 ccxt 下载交易所数据）
2. 传入 `FeatureGenerator(df)`
3. 调用 `generate_all()`，得到带特征的 DataFrame
4. 拿这些特征送入 sklearn / xgboost / pytorch 训练


